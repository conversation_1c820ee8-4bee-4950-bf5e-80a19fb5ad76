<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('playlist_shares', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('playlist_id')->constrained()->onDelete('cascade');
            $table->foreignUlid('shared_by_user_id')->constrained('users')->onDelete('cascade');
            $table->string('share_token', 32)->unique();
            $table->string('platform')->nullable(); // 'link', 'email', 'social', etc.
            $table->text('message')->nullable();
            $table->integer('view_count')->default(0);
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['share_token']);
            $table->index(['playlist_id', 'shared_by_user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('playlist_shares');
    }
};
