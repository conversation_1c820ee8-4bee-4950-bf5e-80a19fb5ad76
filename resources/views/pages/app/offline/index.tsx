import { useState } from "react";
import { AppHead } from "@/components/layouts/app-head";
import { ArticleLook, Button } from "@/components/lib/ui";
import { ItemArticle, PlaylistCard } from "@/components/lib";
import { usePage, router } from "@inertiajs/react";
import { OfflinePageProps } from "@/types/pages";
import { 
    IoCloudDownload, 
    IoTrash, 
    IoWifi, 
    IoCloudOffline,
    IoRefresh 
} from "@/components/lib/icons";
import { fetchApi } from "@/utils";
import { useZiggy } from "@/hooks";

export default function OfflinePage() {
    const { props } = usePage<OfflinePageProps>();
    const { downloaded_articles, downloaded_playlists, download_queue } = props;
    const [loading, setLoading] = useState(false);
    const route = useZiggy();

    const handleClearAll = async () => {
        if (!confirm("Are you sure you want to clear all downloads? This will remove all offline content.")) {
            return;
        }

        setLoading(true);
        try {
            await fetchApi(route("app.offline.clear"), {
                method: "DELETE",
            });
            router.reload();
        } catch (error) {
            console.error("Failed to clear downloads:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleRemoveDownload = async (type: string, id: string) => {
        setLoading(true);
        try {
            await fetchApi(route("app.offline.remove"), {
                method: "DELETE",
                body: JSON.stringify({ type, id }),
            });
            router.reload();
        } catch (error) {
            console.error("Failed to remove download:", error);
        } finally {
            setLoading(false);
        }
    };

    const isOnline = navigator.onLine;
    const totalDownloads = (downloaded_articles?.data?.length || 0) + (downloaded_playlists?.data?.length || 0);

    return (
        <>
            <AppHead title="Offline Music" />
            
            <div className="w-full space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                        <h1 className="text-2xl lg:text-3xl font-bold">Offline Music</h1>
                        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
                            isOnline 
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
                                : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                        }`}>
                            {isOnline ? <IoWifi className="h-4 w-4" /> : <IoCloudOffline className="h-4 w-4" />}
                            <span>{isOnline ? "Online" : "Offline"}</span>
                        </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                        <Button
                            variant="outline"
                            onClick={() => router.reload()}
                            className="flex items-center space-x-2"
                        >
                            <IoRefresh className="h-4 w-4" />
                            <span>Refresh</span>
                        </Button>
                        
                        {totalDownloads > 0 && (
                            <Button
                                variant="danger"
                                onClick={handleClearAll}
                                disabled={loading}
                                className="flex items-center space-x-2"
                            >
                                <IoTrash className="h-4 w-4" />
                                <span>{loading ? "Clearing..." : "Clear All"}</span>
                            </Button>
                        )}
                    </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                        <div className="flex items-center space-x-3">
                            <IoCloudDownload className="h-8 w-8 text-blue-500" />
                            <div>
                                <p className="text-2xl font-bold">{downloaded_articles?.data?.length || 0}</p>
                                <p className="text-gray-600 dark:text-gray-400">Downloaded Songs</p>
                            </div>
                        </div>
                    </div>
                    
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                        <div className="flex items-center space-x-3">
                            <IoCloudDownload className="h-8 w-8 text-green-500" />
                            <div>
                                <p className="text-2xl font-bold">{downloaded_playlists?.data?.length || 0}</p>
                                <p className="text-gray-600 dark:text-gray-400">Downloaded Playlists</p>
                            </div>
                        </div>
                    </div>
                    
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                        <div className="flex items-center space-x-3">
                            <IoRefresh className="h-8 w-8 text-orange-500" />
                            <div>
                                <p className="text-2xl font-bold">{download_queue?.length || 0}</p>
                                <p className="text-gray-600 dark:text-gray-400">In Queue</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Downloaded Songs */}
                {downloaded_articles?.data?.length > 0 && (
                    <section>
                        <h2 className="text-xl font-semibold mb-4">Downloaded Songs</h2>
                        <ArticleLook type="list" cols={1}>
                            {downloaded_articles.data.map((article) => (
                                <div key={article.id} className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <ItemArticle
                                            type="article"
                                            data={article}
                                            viewType="list"
                                            showLikes={true}
                                        />
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleRemoveDownload('article', article.id)}
                                        disabled={loading}
                                        className="ml-4"
                                    >
                                        <IoTrash className="h-4 w-4" />
                                    </Button>
                                </div>
                            ))}
                        </ArticleLook>
                    </section>
                )}

                {/* Downloaded Playlists */}
                {downloaded_playlists?.data?.length > 0 && (
                    <section>
                        <h2 className="text-xl font-semibold mb-4">Downloaded Playlists</h2>
                        <ArticleLook type="card" cols={6}>
                            {downloaded_playlists.data.map((playlist) => (
                                <div key={playlist.id} className="relative">
                                    <PlaylistCard playlist={playlist} />
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleRemoveDownload('playlist', playlist.id)}
                                        disabled={loading}
                                        className="absolute top-2 right-2 p-2"
                                    >
                                        <IoTrash className="h-3 w-3" />
                                    </Button>
                                </div>
                            ))}
                        </ArticleLook>
                    </section>
                )}

                {/* Empty State */}
                {totalDownloads === 0 && (
                    <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <IoCloudDownload className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                            No offline content yet
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                            Download songs and playlists to enjoy them offline.
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">
                            Look for the download button on songs and playlists to save them for offline listening.
                        </p>
                    </div>
                )}
            </div>
        </>
    );
}
