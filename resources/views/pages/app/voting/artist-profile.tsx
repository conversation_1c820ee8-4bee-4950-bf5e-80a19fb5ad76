import { AppContentWrapper, AppHead, AppLayout } from "@/components/layouts";
import { Button, GradientWrapper } from "@/components/lib/ui";
import { Link, router, useForm, usePage } from "@inertiajs/react";
import { useZiggy } from "@/hooks";
import { ArtistProfilePageProps } from "@/types/pages";
import { useState } from "react";
import { getStateName } from "@/utils/competition";

const VOTE_LIMIT = 100; // Maximum votes allowed per transaction

const ArtistProfilePage = () => {
    const { props } = usePage<ArtistProfilePageProps>();
    const { competition, artist, voteCount } = props;
    const route = useZiggy();

    const [selectedVotes, setSelectedVotes] = useState(1);
    const votePrice = +competition.vote_price || 1.0;
    const totalPrice = selectedVotes * votePrice;

    const { setData, processing, post, data } = useForm({
        vote_count: 1,
    });

    const handleVote = async (e: React.FormEvent) => {
        e.preventDefault();
        setData(() => ({
            vote_count: selectedVotes,
        }));
        data.vote_count = selectedVotes;

        const removeEvent = router.on("invalid", (evt) => {
            evt.preventDefault();

            const response = evt.detail.response;
            window.location.href = response.request.responseURL;

            removeEvent();
        });

        post(
            route("app.voting.vote", {
                competition: competition.id,
                artist: artist.id,
            })
        );
    };

    const image = artist.image;

    return (
        <>
            <AppHead title={`${artist.name} - ${competition.name}`} />

            <div className="w-full">
                <div className="border-b border-gray-300/70 dark:border-gray-700">
                    <GradientWrapper image={image}>
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold mb-2">
                                {artist.name}
                            </h1>
                            <p className="text-gray-600 dark:text-gray-300 mb-2">
                                {"Artist in competition"}
                            </p>

                            {/* Competition Badge */}
                            <div className="inline-flex items-center px-4 py-2 bg-primary/10 dark:bg-primary/20 rounded-full mb-4">
                                <div className="text-center">
                                    <div className="text-sm font-medium text-primary-500 dark:text-primary-300">
                                        Competing in
                                    </div>
                                    <div className="text-lg font-bold text-primary-500 dark:text-primary-200">
                                        {competition.name}
                                    </div>
                                    <div className="text-xs text-primary-500/80 dark:text-primary-300/80">
                                        {getStateName(competition.stage)} •{" "}
                                        {competition.type === "refugees_only"
                                            ? "OTM Awards"
                                            : "All Artists"}
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-center space-x-4">
                                <Link
                                    href={route("app.voting.leaderboard", {
                                        competition: competition.id,
                                    })}
                                >
                                    <Button
                                        variant="light"
                                        className="py-1 px-3"
                                    >
                                        Back to Leaderboard
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </GradientWrapper>
                </div>

                <AppContentWrapper>
                    <div className="max-w-4xl mx-auto py-3">
                        <div className="bg-white dark:bg-gray-800/50 shadow-md dark:shadow-none rounded-lg p-6 mb-8">
                            <div className="flex justify-between items-center mb-6">
                                <div>
                                    <h2 className="text-2xl font-bold">
                                        {artist.name}
                                    </h2>
                                    <p className="text-gray-500 dark:text-gray-400">
                                        {artist.role}
                                    </p>
                                </div>
                                <div className="text-center">
                                    <div className="text-3xl font-bold text-primary">
                                        {voteCount}
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                        Votes
                                    </div>
                                </div>
                            </div>

                            {artist.description && (
                                <div className="mb-6">
                                    <h3 className="text-lg font-semibold mb-2">
                                        About
                                    </h3>
                                    <p className="text-gray-700 dark:text-gray-300 break-words overflow-hidden">
                                        {artist.description}
                                    </p>
                                </div>
                            )}

                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                <div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                                        Monthly Listeners
                                    </div>
                                    <div className="text-lg font-semibold">
                                        {artist.monthly_listeners || 0}
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                                        Followers
                                    </div>
                                    <div className="text-lg font-semibold">
                                        {artist.followers_count || 0}
                                    </div>
                                </div>
                                <div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                                        Competition Stage
                                    </div>
                                    <div className="text-lg font-semibold">
                                        {getStateName(competition.stage)}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white dark:bg-gray-800/50 shadow-md dark:shadow-none rounded-lg p-6 mb-8">
                            <h3 className="text-xl font-semibold mb-4">
                                Vote for {artist.name}
                            </h3>

                            <div>
                                <div className="mb-6">
                                    <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4">
                                        <h4 className="text-blue-800 dark:text-blue-300 font-semibold mb-2">
                                            Voting Information
                                        </h4>
                                        <p className="text-gray-700 dark:text-gray-300">
                                            Each vote costs{" "}
                                            <span className="font-bold text-green-600 dark:text-green-400">
                                                ${votePrice.toFixed(2)} USD
                                            </span>
                                        </p>
                                        <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                                            You can vote multiple times to show
                                            your support!
                                        </p>
                                    </div>
                                </div>

                                <form onSubmit={handleVote}>
                                    <div className="space-y-4">
                                        {/* Vote Quantity Selector */}
                                        <div className="flex flex-col items-center space-y-2">
                                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Number of votes
                                            </label>
                                            <div className="flex items-center space-x-3">
                                                <button
                                                    type="button"
                                                    onClick={() =>
                                                        setSelectedVotes(
                                                            Math.max(
                                                                1,
                                                                selectedVotes -
                                                                    1
                                                            )
                                                        )
                                                    }
                                                    className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300"
                                                    disabled={
                                                        selectedVotes <= 1
                                                    }
                                                >
                                                    -
                                                </button>
                                                <span className="text-xl font-semibold w-12 text-center">
                                                    {selectedVotes}
                                                </span>
                                                <button
                                                    type="button"
                                                    onClick={() =>
                                                        setSelectedVotes(
                                                            Math.min(
                                                                VOTE_LIMIT,
                                                                selectedVotes +
                                                                    1
                                                            )
                                                        )
                                                    }
                                                    className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300"
                                                    disabled={
                                                        selectedVotes >=
                                                        VOTE_LIMIT
                                                    }
                                                >
                                                    +
                                                </button>
                                            </div>
                                        </div>

                                        {/* Price Display */}
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-primary">
                                                ${totalPrice.toFixed(2)} USD
                                            </div>
                                            <div className="text-sm text-gray-500 dark:text-gray-400">
                                                Total cost for {selectedVotes}{" "}
                                                vote
                                                {selectedVotes !== 1 ? "s" : ""}
                                            </div>
                                        </div>

                                        {/* Vote Button */}
                                        <div className="flex justify-center">
                                            <Button
                                                type="submit"
                                                variant="primary"
                                                loading={processing}
                                                className="px-8 py-2"
                                            >
                                                Vote Now ($
                                                {totalPrice.toFixed(2)})
                                            </Button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                                <h4 className="text-lg font-semibold mb-3">
                                    Share this Artist
                                </h4>
                                <p className="text-gray-700 dark:text-gray-300 mb-4">
                                    Invite your friends to vote for{" "}
                                    {artist.name} in the {competition.name}{" "}
                                    competition.
                                </p>

                                <div className="flex justify-center">
                                    <Button
                                        variant="light"
                                        className="px-6"
                                        onClick={() => {
                                            const shareUrl =
                                                window.location.href;
                                            navigator.clipboard.writeText(
                                                shareUrl
                                            );
                                            alert("Link copied to clipboard!");
                                        }}
                                    >
                                        Copy Share Link
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </AppContentWrapper>
            </div>
        </>
    );
};

ArtistProfilePage.layout = (page: any) => <AppLayout children={page} />;

export default ArtistProfilePage;
