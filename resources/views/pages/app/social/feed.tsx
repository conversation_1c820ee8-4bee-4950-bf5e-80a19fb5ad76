import { useState } from "react";
import { AppHead } from "@/components/layouts/app-head";
import { Button, ArticleLook } from "@/components/lib/ui";
import { ItemArticle, UserCard } from "@/components/lib";
import { Link, usePage, router } from "@inertiajs/react";
import { SocialFeedPageProps } from "@/types/pages";
import {
    IoHeart,
    IoHeartOutline,
    IoMusicalNotes,
    IoPersonAdd,
    IoShare,
    IoTime,
    IoTrendingUp,
    IoRefresh,
} from "@/components/lib/icons";
import { asset, cn, fetchApi } from "@/utils";
import { useZiggy } from "@/hooks";

export default function SocialFeedPage() {
    const { props } = usePage<SocialFeedPageProps>();
    const { activities, suggested_users, trending_artists } = props;
    const [loading, setLoading] = useState(false);
    const route = useZiggy();

    const handleRefresh = async () => {
        setLoading(true);
        try {
            router.reload();
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <AppHead title="Social Feed" />

            <div className="w-full space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl lg:text-3xl font-bold">
                        Social Feed
                    </h1>
                    <div className="flex items-center space-x-3">
                        <Button
                            variant="outline"
                            onClick={handleRefresh}
                            disabled={loading}
                            className="flex items-center space-x-2"
                        >
                            <IoRefresh
                                className={cn(
                                    "h-4 w-4",
                                    loading && "animate-spin"
                                )}
                            />
                            <span>Refresh</span>
                        </Button>
                        <Link href={route("app.social.discover")}>
                            <Button
                                variant="primary"
                                className="flex items-center space-x-2"
                            >
                                <IoPersonAdd className="h-4 w-4" />
                                <span>Discover</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Main Feed */}
                    <div className="lg:col-span-3 space-y-6">
                        {activities?.data?.length > 0 ? (
                            <div className="space-y-4">
                                {activities.data.map((activity) => (
                                    <ActivityCard
                                        key={activity.id}
                                        activity={activity}
                                    />
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <IoMusicalNotes className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                    No activity yet
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400 mb-4">
                                    Follow some artists to see their activity
                                    here.
                                </p>
                                <Link href={route("app.social.discover")}>
                                    <Button variant="primary">
                                        Discover Artists
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Suggested Users */}
                        {suggested_users?.data?.length > 0 && (
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                                <h3 className="text-lg font-semibold mb-4 flex items-center">
                                    <IoPersonAdd className="h-5 w-5 mr-2" />
                                    Suggested for You
                                </h3>
                                <div className="space-y-3">
                                    {suggested_users.data
                                        .slice(0, 5)
                                        .map((user) => (
                                            <SuggestedUserCard
                                                key={user.id}
                                                user={user}
                                            />
                                        ))}
                                </div>
                                <Link
                                    href={route("app.social.discover")}
                                    className="block mt-4"
                                >
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full"
                                    >
                                        See More
                                    </Button>
                                </Link>
                            </div>
                        )}

                        {/* Trending Artists */}
                        {trending_artists?.data?.length > 0 && (
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                                <h3 className="text-lg font-semibold mb-4 flex items-center">
                                    <IoTrendingUp className="h-5 w-5 mr-2" />
                                    Trending This Week
                                </h3>
                                <div className="space-y-3">
                                    {trending_artists.data
                                        .slice(0, 5)
                                        .map((artist, index) => (
                                            <TrendingArtistCard
                                                key={artist.id}
                                                artist={artist}
                                                rank={index + 1}
                                            />
                                        ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}

interface ActivityCardProps {
    activity: any;
}

function ActivityCard({ activity }: ActivityCardProps) {
    const route = useZiggy();

    const getActivityIcon = (type: string) => {
        switch (type) {
            case "like":
                return <IoHeart className="h-5 w-5 text-red-500" />;
            case "follow":
                return <IoPersonAdd className="h-5 w-5 text-blue-500" />;
            case "share":
                return <IoShare className="h-5 w-5 text-green-500" />;
            default:
                return <IoMusicalNotes className="h-5 w-5 text-gray-500" />;
        }
    };

    const getActivityText = (activity: any) => {
        switch (activity.type) {
            case "like":
                return `liked ${activity.subject?.name || "a track"}`;
            case "follow":
                return `started following ${
                    activity.data?.target_user_name || "someone"
                }`;
            case "share":
                return `shared ${activity.subject?.name || "content"}`;
            default:
                return "had some activity";
        }
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <div className="flex items-start space-x-4">
                {/* User Avatar */}
                <Link
                    href={route("app.artists.show", { user: activity.user.id })}
                >
                    <img
                        src={
                            asset(activity.user.image) || "/default-avatar.png"
                        }
                        alt={activity.user.name}
                        className="h-12 w-12 rounded-full object-cover"
                    />
                </Link>

                <div className="flex-1 min-w-0">
                    {/* Activity Header */}
                    <div className="flex items-center space-x-2 mb-2">
                        {getActivityIcon(activity.type)}
                        <p className="text-sm text-gray-900 dark:text-gray-100">
                            <Link
                                href={route("app.artists.show", {
                                    user: activity.user.id,
                                })}
                                className="font-medium hover:text-blue-600 dark:hover:text-blue-400"
                            >
                                {activity.user.name}
                            </Link>{" "}
                            {getActivityText(activity)}
                        </p>
                        <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <IoTime className="h-3 w-3 mr-1" />
                            {new Date(activity.created_at).toLocaleDateString()}
                        </span>
                    </div>

                    {/* Activity Content */}
                    {activity.subject && activity.type === "like" && (
                        <div className="mt-3">
                            <ItemArticle
                                type="article"
                                data={activity.subject}
                                viewType="list"
                                showLikes={true}
                            />
                        </div>
                    )}

                    {activity.data?.message && (
                        <p className="text-gray-700 dark:text-gray-300 mt-2">
                            {activity.data.message}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
}

interface SuggestedUserCardProps {
    user: any;
}

function SuggestedUserCard({ user }: SuggestedUserCardProps) {
    const [following, setFollowing] = useState(false);
    const [loading, setLoading] = useState(false);
    const route = useZiggy();

    const handleFollow = async () => {
        setLoading(true);
        try {
            const response = await fetchApi(
                route("app.social.follow", { user: user.id }),
                {
                    method: "POST",
                }
            );
            setFollowing(response.is_following);
        } catch (error) {
            console.error("Failed to follow user:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
                <Link href={route("app.artists.show", { user: user.id })}>
                    <img
                        src={asset(user.image) || "/default-avatar.png"}
                        alt={user.name}
                        className="h-10 w-10 rounded-full object-cover"
                    />
                </Link>
                <div>
                    <Link
                        href={route("app.artists.show", { user: user.id })}
                        className="font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400"
                    >
                        {user.name}
                    </Link>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                        {user.articles_count} tracks
                    </p>
                </div>
            </div>
            <Button
                variant={following ? "outline" : "primary"}
                size="sm"
                onClick={handleFollow}
                disabled={loading}
            >
                {following ? "Following" : "Follow"}
            </Button>
        </div>
    );
}

interface TrendingArtistCardProps {
    artist: any;
    rank: number;
}

function TrendingArtistCard({ artist, rank }: TrendingArtistCardProps) {
    const route = useZiggy();

    return (
        <div className="flex items-center space-x-3">
            <span className="text-lg font-bold text-gray-400 w-6">#{rank}</span>
            <Link href={route("app.artists.show", { user: artist.id })}>
                <img
                    src={asset(artist.image) || "/default-avatar.png"}
                    alt={artist.name}
                    className="h-10 w-10 rounded-full object-cover"
                />
            </Link>
            <div className="flex-1 min-w-0">
                <Link
                    href={route("app.artists.show", { user: artist.id })}
                    className="font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 truncate block"
                >
                    {artist.name}
                </Link>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                    {artist.recent_plays_count} plays this week
                </p>
            </div>
        </div>
    );
}
