import { AppHead } from "@/components/layouts/app-head";
import { ArticleLook, Button } from "@/components/lib/ui";
import { PlaylistCard } from "@/components/lib";
import { usePlaylistManagement } from "@/components/lib/playlist-management";
import { usePage } from "@inertiajs/react";
import { LibraryPageProps } from "@/types/pages";
import { IoAdd } from "@/components/lib/icons";

export default function LibraryPage() {
    const { props } = usePage<LibraryPageProps>();
    const { user_playlists, liked_playlists, recent_playlists } = props;
    
    const { 
        createModal, 
        CreatePlaylistModal 
    } = usePlaylistManagement();

    return (
        <>
            <AppHead title="Your Library" />
            
            <div className="w-full space-y-8">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl lg:text-3xl font-bold">Your Library</h1>
                    <Button
                        variant="primary"
                        onClick={createModal.openModal}
                        className="flex items-center space-x-2"
                    >
                        <IoAdd className="h-5 w-5" />
                        <span>Create Playlist</span>
                    </Button>
                </div>

                {/* Your Playlists */}
                <section>
                    <h2 className="text-xl font-semibold mb-4">Your Playlists</h2>
                    {user_playlists?.data?.length > 0 ? (
                        <ArticleLook type="card" cols={6}>
                            {user_playlists.data.map((playlist) => (
                                <PlaylistCard
                                    key={playlist.id}
                                    playlist={playlist}
                                    showActions={true}
                                />
                            ))}
                        </ArticleLook>
                    ) : (
                        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                You haven't created any playlists yet
                            </p>
                            <Button
                                variant="primary"
                                onClick={createModal.openModal}
                                className="flex items-center space-x-2 mx-auto"
                            >
                                <IoAdd className="h-5 w-5" />
                                <span>Create Your First Playlist</span>
                            </Button>
                        </div>
                    )}
                </section>

                {/* Liked Playlists */}
                {liked_playlists?.data?.length > 0 && (
                    <section>
                        <h2 className="text-xl font-semibold mb-4">Liked Playlists</h2>
                        <ArticleLook type="card" cols={6}>
                            {liked_playlists.data.map((playlist) => (
                                <PlaylistCard
                                    key={playlist.id}
                                    playlist={playlist}
                                />
                            ))}
                        </ArticleLook>
                    </section>
                )}

                {/* Recently Played */}
                {recent_playlists?.data?.length > 0 && (
                    <section>
                        <h2 className="text-xl font-semibold mb-4">Recently Played</h2>
                        <ArticleLook type="card" cols={6}>
                            {recent_playlists.data.map((playlist) => (
                                <PlaylistCard
                                    key={playlist.id}
                                    playlist={playlist}
                                />
                            ))}
                        </ArticleLook>
                    </section>
                )}
            </div>

            {/* Create Playlist Modal */}
            <CreatePlaylistModal
                isOpen={createModal.isOpen}
                onClose={createModal.closeModal}
                onSuccess={(playlist) => {
                    console.log('Playlist created:', playlist);
                }}
            />
        </>
    );
}
