import { AppHead } from "@/components/layouts/app-head";
import { But<PERSON> } from "@/components/lib/ui";
import { Link, usePage } from "@inertiajs/react";
import { useZiggy } from "@/hooks";
import { MessagesPageProps } from "@/types/pages";
import { IoSend, IoPersonAdd } from "@/components/lib/icons";
import { asset, cn } from "@/utils";

export default function MessagesPage() {
    const { props } = usePage<MessagesPageProps>();
    const { conversations } = props;
    const route = useZiggy();

    return (
        <>
            <AppHead title="Messages" />
            
            <div className="w-full space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl lg:text-3xl font-bold">Messages</h1>
                    <Button
                        variant="primary"
                        className="flex items-center space-x-2"
                    >
                        <IoPersonAdd className="h-5 w-5" />
                        <span>New Message</span>
                    </Button>
                </div>

                {/* Conversations List */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                    {conversations?.data?.length > 0 ? (
                        <div className="divide-y divide-gray-200 dark:divide-gray-700">
                            {conversations.data.map((conversation) => (
                                <ConversationItem
                                    key={conversation.id}
                                    conversation={conversation}
                                />
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <IoSend className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                No conversations yet
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                Start a conversation with other users to see them here.
                            </p>
                            <Button
                                variant="primary"
                                className="flex items-center space-x-2 mx-auto"
                            >
                                <IoPersonAdd className="h-5 w-5" />
                                <span>Start a Conversation</span>
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}

interface ConversationItemProps {
    conversation: any;
}

function ConversationItem({ conversation }: ConversationItemProps) {
    const route = useZiggy();
    const otherUser = conversation.other_user;
    const lastMessage = conversation.last_message;
    const hasUnread = conversation.unread_count > 0;

    return (
        <Link
            href={route("app.messages.show", { conversation: conversation.id })}
            className={cn(
                "block p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                hasUnread && "bg-blue-50 dark:bg-blue-900/20"
            )}
        >
            <div className="flex items-center space-x-4">
                {/* Avatar */}
                <div className="flex-shrink-0">
                    <img
                        src={asset(otherUser.image) || "/default-avatar.png"}
                        alt={otherUser.name}
                        className="h-12 w-12 rounded-full object-cover"
                    />
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <h3 className={cn(
                            "text-sm font-medium truncate",
                            hasUnread 
                                ? "text-gray-900 dark:text-gray-100" 
                                : "text-gray-700 dark:text-gray-300"
                        )}>
                            {otherUser.name}
                        </h3>
                        
                        {lastMessage && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {new Date(lastMessage.created_at).toLocaleDateString()}
                            </span>
                        )}
                    </div>

                    {lastMessage && (
                        <div className="flex items-center justify-between mt-1">
                            <p className={cn(
                                "text-sm truncate",
                                hasUnread 
                                    ? "text-gray-900 dark:text-gray-100 font-medium" 
                                    : "text-gray-600 dark:text-gray-400"
                            )}>
                                {lastMessage.content}
                            </p>
                            
                            {hasUnread && (
                                <span className="ml-2 bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                    {conversation.unread_count}
                                </span>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </Link>
    );
}
