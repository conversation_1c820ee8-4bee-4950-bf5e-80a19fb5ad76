import { useState, useEffect, useRef, FormEvent } from "react";
import { AppHead } from "@/components/layouts/app-head";
import { Button } from "@/components/lib/ui";
import { Link, usePage, router } from "@inertiajs/react";
import { useZiggy } from "@/hooks";
import { ConversationPageProps } from "@/types/pages";
import { IoSend, IoArrowBack } from "@/components/lib/icons";
import { asset, cn, fetchApi } from "@/utils";

export default function ConversationPage() {
    const { props } = usePage<ConversationPageProps>();
    const { conversation, messages } = props;
    const route = useZiggy();
    
    const [messageContent, setMessageContent] = useState("");
    const [sending, setSending] = useState(false);
    const [messagesList, setMessagesList] = useState(messages?.data || []);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const otherUser = conversation.other_user;

    // Scroll to bottom when messages change
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messagesList]);

    const handleSendMessage = async (e: FormEvent) => {
        e.preventDefault();
        if (!messageContent.trim() || sending) return;

        setSending(true);
        try {
            const response = await fetchApi(route("app.messages.store"), {
                method: "POST",
                body: JSON.stringify({
                    recipient_id: otherUser.id,
                    content: messageContent.trim(),
                }),
            });

            // Add the new message to the list
            setMessagesList(prev => [...prev, response]);
            setMessageContent("");
            
            // Refresh the page data
            router.reload({ only: ['messages'] });
        } catch (error) {
            console.error("Failed to send message:", error);
        } finally {
            setSending(false);
        }
    };

    return (
        <>
            <AppHead title={`Chat with ${otherUser.name}`} />
            
            <div className="flex flex-col h-[calc(100vh-200px)] bg-white dark:bg-gray-800 rounded-lg shadow">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                        <Link
                            href={route("app.messages.index")}
                            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                        >
                            <IoArrowBack className="h-5 w-5" />
                        </Link>
                        
                        <img
                            src={asset(otherUser.image) || "/default-avatar.png"}
                            alt={otherUser.name}
                            className="h-10 w-10 rounded-full object-cover"
                        />
                        
                        <div>
                            <h2 className="font-semibold text-gray-900 dark:text-gray-100">
                                {otherUser.name}
                            </h2>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {otherUser.role}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {messagesList.length > 0 ? (
                        messagesList.map((message) => (
                            <MessageBubble
                                key={message.id}
                                message={message}
                                isOwn={message.sender.id === props.auth?.user?.id}
                            />
                        ))
                    ) : (
                        <div className="text-center py-8">
                            <p className="text-gray-600 dark:text-gray-400">
                                No messages yet. Start the conversation!
                            </p>
                        </div>
                    )}
                    <div ref={messagesEndRef} />
                </div>

                {/* Message Input */}
                <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex space-x-3">
                        <input
                            type="text"
                            value={messageContent}
                            onChange={(e) => setMessageContent(e.target.value)}
                            placeholder="Type your message..."
                            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            disabled={sending}
                        />
                        <Button
                            type="submit"
                            variant="primary"
                            disabled={!messageContent.trim() || sending}
                            className="flex items-center space-x-2"
                        >
                            <IoSend className="h-4 w-4" />
                            <span>{sending ? "Sending..." : "Send"}</span>
                        </Button>
                    </div>
                </form>
            </div>
        </>
    );
}

interface MessageBubbleProps {
    message: any;
    isOwn: boolean;
}

function MessageBubble({ message, isOwn }: MessageBubbleProps) {
    return (
        <div className={cn("flex", isOwn ? "justify-end" : "justify-start")}>
            <div className={cn("flex items-end space-x-2 max-w-xs lg:max-w-md")}>
                {!isOwn && (
                    <img
                        src={asset(message.sender.image) || "/default-avatar.png"}
                        alt={message.sender.name}
                        className="h-6 w-6 rounded-full object-cover"
                    />
                )}
                
                <div className={cn(
                    "px-4 py-2 rounded-lg",
                    isOwn 
                        ? "bg-blue-500 text-white" 
                        : "bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                )}>
                    <p className="text-sm">{message.content}</p>
                    <p className={cn(
                        "text-xs mt-1",
                        isOwn 
                            ? "text-blue-100" 
                            : "text-gray-500 dark:text-gray-400"
                    )}>
                        {new Date(message.created_at).toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                        })}
                        {isOwn && message.is_read && (
                            <span className="ml-1">✓</span>
                        )}
                    </p>
                </div>
                
                {isOwn && (
                    <img
                        src={asset(message.sender.image) || "/default-avatar.png"}
                        alt={message.sender.name}
                        className="h-6 w-6 rounded-full object-cover"
                    />
                )}
            </div>
        </div>
    );
}
