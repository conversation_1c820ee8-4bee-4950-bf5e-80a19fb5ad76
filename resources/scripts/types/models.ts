export interface Article {
    // columns
    id: string;
    name: string;
    description: string;
    genre_id?: string | null;
    channel_id: string;
    user_id: string;
    created_at?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
    liked?: boolean;
    likes_count?: number;
    // relations
    user?: User;
    genre?: Genre;
    channel: Channel;
    image?: Image;
    audio?: Audio;
    comments?: Comment[];
    likes?: Like[];
    downloads?: Download[];
    featurings?: Artist[];
    plays?: Play[];
    __typename: "article";
}

export interface ArtistRequest {
    // columns
    id: string;
    user_id: string;
    description: string;
    artist_type: UserRole;
    approved: boolean;
    created_at?: string | null;
    updated_at?: string | null;
    // relations
    user?: User;
}

export interface Notification {
    id: string;
    actions: any[];
    body: string;
    duration: string;
    icon: string;
    iconColor: string;
    title: string;
    view: string;
    viewData: any[];
    created_at: string;
    readable_date: string;
}

export interface Genre {
    // columns
    id: string;
    name: string;
    category: string;
    created_at?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
    // relations
    image?: Image;
    channels?: Channel[];
    articles?: Article[];
}

export type PlaylistGenerationStrategy =
    | "following"
    | "recent_play_similarity"
    | "like_similarity"
    | "popular_genre"
    | "popular_global"
    | "top_listened";

export interface Playlist {
    // Columns
    id: string;
    name: string;
    description?: string | null;
    liked?: boolean;
    is_system_generated?: boolean;
    generation_strategy?: PlaylistGenerationStrategy;
    generated_at?: string | null;
    created_at?: string | null;
    // Relations
    user?: User;
    image?: Image;
    fallback_image?: Image;
    __typename: "playlist";
}

export interface Channel {
    // columns
    id: string;
    name: string;
    description?: string | null;
    type: ChannelType;
    genre_id?: string | null;
    user_id: string;
    created_at?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
    liked?: boolean;
    likes_count?: number;
    articles_count?: number;
    comments_count?: number;
    downloads_count?: number;
    // relations
    user?: User;
    genre?: Genre;
    image?: Image;
    likes?: Like[];
    downloads?: Download[];
    comments?: Comment[];
    articles?: Article[];
    __typename: "channel";
}

export interface Featuring {
    // columns
    id: string;
    article_id: string;
    user_id: string;
    created_at?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
    // relations
    user?: User;
    article?: Article;
}

export interface Audio {
    // columns
    id: string;
    title: string;
    storage: string;
    filename_disk: string;
    filename_download: string;
    type: string;
    filesize: number;
    location: string;
    url?: string | null;
    width?: number | null;
    height?: number | null;
    duration?: number | null;
    description?: string | null;
    tags: string;
    metadata: string;
    hls_enabled?: boolean;
    hls_directory?: string | null;
    fileable_type: string;
    fileable_id: string;
    created_by?: string | null;
    updated_by?: string | null;
    created_at?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
}

export interface Image {
    // columns
    id: string;
    title: string;
    storage: string;
    filename_disk: string;
    filename_download: string;
    type: string;
    filesize: number;
    location: string;
    url?: string | null;
    width?: number | null;
    height?: number | null;
    duration?: number | null;
    description?: string | null;
    tags: string;
    metadata: string;
    fileable_type: string;
    fileable_id: string;
    created_by?: string | null;
    updated_by?: string | null;
    created_at?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
}

export interface Following {
    // columns
    id: string;
    user_id: string;
    follower_id: string;
    created_at?: string | null;
    updated_at?: string | null;
    // relations
    user?: User;
    follower?: User;
}

export interface Comment {
    // columns
    id: string;
    comment: string;
    commentable_type: string;
    commentable_id: string;
    user_id: string;
    created_at?: string | null;
    readable_date?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
    // relations
    user?: User;
    commentable?: Comment;
}

export interface Download {
    // columns
    id: number;
    downloadable_type: string;
    downloadable_id: string;
    user_id: string;
    created_at?: string | null;
    updated_at?: string | null;
    // relations
    downloadable?: Download;
    user?: User;
}

export interface File {
    // columns
    id: string;
    title: string;
    storage: string;
    filename_disk: string;
    filename_download: string;
    type: string;
    filesize: number;
    location: string;
    url?: string | null;
    width?: number | null;
    height?: number | null;
    duration?: number | null;
    description?: string | null;
    tags: string;
    metadata: string;
    fileable_type: string;
    fileable_id: string;
    created_at?: string | null;
    updated_at?: string | null;
    deleted_at?: string | null;
    // relations
    fileable?: File;
    created_by?: User;
    updated_by?: User;
}

export interface Like {
    // columns
    id: number;
    likeable_type: string;
    likeable_id: string;
    user_id: string;
    created_at?: string | null;
    updated_at?: string | null;
    // relations
    likeable?: Like;
    user?: User;
}

export interface Play {
    // columns
    id: number;
    article_id: string;
    user_id: string;
    player_id?: string | null;
    created_at?: string | null;
    updated_at?: string | null;
    // relations
    user?: User;
    player?: User;
    article?: Article;
}

export interface User {
    // columns
    id: string;
    name: string;
    email: string;
    email_verified_at?: string;
    verified?: boolean;
    role: UserRole;
    phone?: string;
    country_name?: string;
    country_code?: string;
    timezone?: string;
    city?: string;
    region?: string;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
    description?: string;
    has_verified_email?: boolean;
    // onboarding fields
    has_completed_onboarding?: boolean;
    is_new_user?: boolean;
    should_show_welcome?: boolean;
    last_activity_at?: string;
    onboarding_completed_at?: string;
    welcome_popup_shown_at?: string;
    // resources
    followers_count?: null | string;
    followings_count?: string;
    // mutators
    monthly_plays?: number;
    monthly_listeners?: number;
    // competition leaderboard fields
    votes_count?: number;
    monthly_listeners_count?: number;
    composite_score?: number;
    // relations
    image?: Image;
    channels?: Channel[];
    articles?: Article[];
    plays?: Play[];
    playings?: Play[];
    comments?: Comment[];
    featurings?: Featuring[];
    followers?: Following[];
    followings?: Following[];
    artist_request?: ArtistRequest;
}

export type Artist = Pick<
    User,
    | "id"
    | "image"
    | "name"
    | "role"
    | "description"
    | "followers_count"
    | "followings_count"
>;

const ChannelType = {
    CHANNEL: "CHANNEL",
    ALBUM: "ALBUM",
    SINGLE: "SINGLE",
} as const;

export type ChannelType = (typeof ChannelType)[keyof typeof ChannelType];

const UserRole = {
    GUEST: "GUEST",
    SINGER: "SINGER",
    PODCASTER: "PODCASTER",
    ADMIN: "ADMIN",
} as const;

export type UserRole = (typeof UserRole)[keyof typeof UserRole];

export interface Competition {
    // columns
    id: string;
    name: string;
    description?: string;
    start_date: string;
    end_date: string;
    status: boolean;
    type: string; // 'refugees_only' or 'all'
    stage: number; // 1-5
    vote_price: number; // Price per vote in USD
    auto_approve: boolean; // Auto-approve entries that meet requirements
    requirements?: {
        min_followers: number;
        min_monthly_listeners: number;
        min_tracks: number;
    };
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
    // computed attributes (server-side)
    is_in_voting_phase: boolean;
    is_accepting_entries: boolean;
    is_in_entry_phase: boolean;
    phase_status: 'inactive' | 'entry_phase' | 'voting_phase' | 'ended';
    // relations
    image?: Image;
    entries?: CompetitionEntry[];
    votes?: Vote[];
}

export interface CompetitionEntry {
    // columns
    id: string;
    competition_id: string;
    user_id: string;
    status: string; // 'pending', 'approved', 'rejected'
    entry_date: string;
    requirements_met: boolean;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
    // relations
    competition?: Competition;
    user?: User;
}

export interface Vote {
    // columns
    id: string;
    competition_id: string;
    user_id: string;
    artist_id: string;
    vote_count: number;
    paid: boolean;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
    // relations
    competition?: Competition;
    user?: User;
    artist?: User;
    transaction?: VoteTransaction;
}

export interface VoteTransaction {
    // columns
    id: string;
    vote_id: string;
    amount: number;
    transaction_id?: string;
    status: string; // 'pending', 'completed', 'failed'
    provider: string;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
    // relations
    vote?: Vote;
}

export interface LegalDocument {
    // columns
    id: string;
    type: string; // 'terms_of_service', 'privacy_policy', 'competition_rules'
    title: string;
    content: string;
    version: number;
    is_active: boolean;
    effective_date: string;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
}
