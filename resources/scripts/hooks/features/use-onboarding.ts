import { useState, useEffect, useCallback } from "react";
import { useAuthUser } from "@/hooks";
import { useToasts } from "@/components/lib";
import { fetchApi } from "@/utils";

interface OnboardingState {
    shouldShowWelcome: boolean;
    isNewUser: boolean;
    hasCompletedOnboarding: boolean;
    lastActivityAt: string | null;
    welcomePopupShownAt: string | null;
}

interface UseOnboardingReturn {
    showWelcomeModal: boolean;
    isLoading: boolean;
    markWelcomeShown: () => Promise<void>;
    completeOnboarding: () => Promise<void>;
    updateActivity: () => Promise<void>;
    onboardingState: OnboardingState | null;
}

export function useOnboarding(): UseOnboardingReturn {
    const authUser = useAuthUser();
    const { pushToast } = useToasts();
    const [showWelcomeModal, setShowWelcomeModal] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [onboardingState, setOnboardingState] =
        useState<OnboardingState | null>(null);

    // Don't show modal if user is not authenticated
    if (!authUser) {
        return {
            showWelcomeModal: false,
            isLoading: false,
            markWelcomeShown: async () => {},
            completeOnboarding: async () => {},
            updateActivity: async () => {},
            onboardingState: null,
        };
    }

    // Fetch onboarding state from server
    const fetchOnboardingState = useCallback(async () => {
        if (!authUser) return;

        try {
            setIsLoading(true);
            const { data } = await fetchApi<any>("/app/user/onboarding/status");

            setOnboardingState(data);
            setShowWelcomeModal(data.shouldShowWelcome);
        } catch (error) {
            console.error("Failed to fetch onboarding state:", error);
        } finally {
            setIsLoading(false);
        }
    }, [authUser]);

    // Mark welcome popup as shown
    const markWelcomeShown = useCallback(async () => {
        if (!authUser) return;

        try {
            await fetchApi("/app/user/onboarding/welcome-shown", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            setOnboardingState((prev) => {
                return prev
                    ? {
                          ...prev,
                          welcomePopupShownAt: new Date().toISOString(),
                      }
                    : null;
            });
            setShowWelcomeModal(false);
        } catch (error) {
            console.error("Failed to mark welcome as shown:", error);
            pushToast({
                type: "error",
                title: "Failed to update welcome status",
            });
        }
    }, [authUser, pushToast]);

    // Complete onboarding
    const completeOnboarding = useCallback(async () => {
        if (!authUser) return;

        try {
            await fetchApi("/app/user/onboarding/complete", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            setOnboardingState((prev) =>
                prev
                    ? {
                          ...prev,
                          hasCompletedOnboarding: true,
                          shouldShowWelcome: false,
                      }
                    : null
            );
            setShowWelcomeModal(false);

            pushToast({
                type: "success",
                title: "Welcome to Smovee! Enjoy exploring our platform.",
            });
        } catch (error) {
            console.error("Failed to complete onboarding:", error);
            pushToast({
                type: "error",
                title: "Failed to complete onboarding",
            });
        }
    }, [authUser, pushToast]);

    // Update user activity
    const updateActivity = useCallback(async () => {
        if (!authUser) return;

        try {
            await fetchApi("/app/user/activity/update", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            setOnboardingState((prev) =>
                prev
                    ? {
                          ...prev,
                          lastActivityAt: new Date().toISOString(),
                      }
                    : null
            );
        } catch (error) {
            // Silently fail for activity updates to avoid disrupting user experience
            console.error("Failed to update activity:", error);
        }
    }, [authUser]);

    // Initialize onboarding state when user is available
    useEffect(() => {
        if (authUser) {
            fetchOnboardingState();
        } else {
            setOnboardingState(null);
            setShowWelcomeModal(false);
        }
    }, [authUser, fetchOnboardingState]);

    // Update activity on mount and periodically
    useEffect(() => {
        if (authUser) {
            updateActivity();

            // Update activity every 5 minutes
            const interval = setInterval(updateActivity, 5 * 60 * 1000);

            return () => clearInterval(interval);
        }
    }, [authUser, updateActivity]);

    // Handle page visibility change to update activity
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden && authUser) {
                updateActivity();
            }
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
            document.removeEventListener(
                "visibilitychange",
                handleVisibilityChange
            );
        };
    }, [authUser, updateActivity]);

    return {
        showWelcomeModal,
        isLoading,
        markWelcomeShown,
        completeOnboarding,
        updateActivity,
        onboardingState,
    };
}
