import { useState } from "react";
import { Link, usePage } from "@inertiajs/react";
import { useZiggy, useAuthUser } from "@/hooks";
import { cn } from "@/utils";
import {
    IoGridOutline,
    IoSearchOutline,
    IoHeartOutline,
    IoLibraryOutline,
    IoChatbubbleOutline,
    IoCloudDownload,
    IoPersonOutline,
    IoGrid,
    IoSearch,
    IoHeart,
    IoLibrary,
    IoChatbubble,
    IoCloudOffline,
    IoPerson,
} from "@/components/lib/icons";

interface NavItem {
    text: string;
    href: string;
    Icon: React.ComponentType<{ className?: string }>;
    ActiveIcon: React.ComponentType<{ className?: string }>;
    active: boolean;
}

export function MobileNavigation() {
    const route = useZiggy();
    const auth = useAuthUser();
    const { url } = usePage();

    const isActive = (href: string) => {
        if (href === route("app.discover")) {
            return url === href || url === "/app" || url === "/";
        }
        return url.startsWith(href);
    };

    const navItems: NavItem[] = [
        {
            text: "Discover",
            href: route("app.discover"),
            Icon: IoGridOutline,
            ActiveIcon: IoGrid,
            active: true,
        },
        {
            text: "Search",
            href: route("app.search"),
            Icon: IoSearchOutline,
            ActiveIcon: IoSearch,
            active: true,
        },
        {
            text: "Library",
            href: route("app.library"),
            Icon: IoLibraryOutline,
            ActiveIcon: IoLibrary,
            active: !!auth,
        },
        {
            text: "Offline",
            href: route("app.offline.index"),
            Icon: IoCloudDownload,
            ActiveIcon: IoCloudOffline,
            active: !!auth,
        },
        {
            text: "Profile",
            href: auth ? route("app.artists.show", { user: auth.id }) : route("login"),
            Icon: IoPersonOutline,
            ActiveIcon: IoPerson,
            active: true,
        },
    ];

    return (
        <nav className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 z-50 md:hidden">
            <div className="flex justify-around items-center py-2">
                {navItems.map((item) => {
                    if (!item.active) return null;

                    const isCurrentlyActive = isActive(item.href);
                    const IconComponent = isCurrentlyActive ? item.ActiveIcon : item.Icon;

                    return (
                        <Link
                            key={item.text}
                            href={item.href}
                            className={cn(
                                "flex flex-col items-center justify-center px-3 py-2 rounded-lg transition-colors min-w-0 flex-1",
                                isCurrentlyActive
                                    ? "text-blue-600 dark:text-blue-400"
                                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                            )}
                        >
                            <IconComponent className="h-6 w-6 mb-1" />
                            <span className="text-xs font-medium truncate">
                                {item.text}
                            </span>
                        </Link>
                    );
                })}
            </div>
        </nav>
    );
}

// Mobile header component
export function MobileHeader() {
    const auth = useAuthUser();
    const route = useZiggy();
    const [showNotifications, setShowNotifications] = useState(false);

    return (
        <header className="sticky top-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 z-40 md:hidden">
            <div className="flex items-center justify-between px-4 py-3">
                {/* Logo */}
                <Link href={route("app.discover")} className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-sm">S</span>
                    </div>
                    <span className="font-bold text-lg">Smovee</span>
                </Link>

                {/* Right side actions */}
                <div className="flex items-center space-x-3">
                    {auth && (
                        <>
                            {/* Messages */}
                            <Link
                                href={route("app.messages.index")}
                                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                            >
                                <IoChatbubbleOutline className="h-6 w-6" />
                            </Link>

                            {/* Notifications */}
                            <button
                                onClick={() => setShowNotifications(!showNotifications)}
                                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors relative"
                            >
                                <IoHeartOutline className="h-6 w-6" />
                                {/* Notification badge */}
                                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    3
                                </span>
                            </button>
                        </>
                    )}

                    {/* Search */}
                    <Link
                        href={route("app.search")}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                    >
                        <IoSearchOutline className="h-6 w-6" />
                    </Link>
                </div>
            </div>
        </header>
    );
}

// Mobile-optimized layout wrapper
export function MobileLayout({ children }: { children: React.ReactNode }) {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:hidden">
            <MobileHeader />
            <main className="pb-20 pt-4">
                {children}
            </main>
            <MobileNavigation />
        </div>
    );
}

// Pull-to-refresh component
export function PullToRefresh({ onRefresh, children }: { 
    onRefresh: () => Promise<void>; 
    children: React.ReactNode;
}) {
    const [isPulling, setIsPulling] = useState(false);
    const [pullDistance, setPullDistance] = useState(0);
    const [startY, setStartY] = useState(0);

    const handleTouchStart = (e: React.TouchEvent) => {
        setStartY(e.touches[0].clientY);
    };

    const handleTouchMove = (e: React.TouchEvent) => {
        const currentY = e.touches[0].clientY;
        const distance = currentY - startY;

        if (distance > 0 && window.scrollY === 0) {
            e.preventDefault();
            setPullDistance(Math.min(distance, 100));
            setIsPulling(distance > 60);
        }
    };

    const handleTouchEnd = async () => {
        if (isPulling) {
            try {
                await onRefresh();
            } catch (error) {
                console.error("Refresh failed:", error);
            }
        }
        setPullDistance(0);
        setIsPulling(false);
        setStartY(0);
    };

    return (
        <div
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            className="relative"
        >
            {/* Pull indicator */}
            {pullDistance > 0 && (
                <div 
                    className="absolute top-0 left-0 right-0 flex items-center justify-center bg-blue-50 dark:bg-blue-900/20 transition-all duration-200"
                    style={{ height: `${pullDistance}px` }}
                >
                    <div className={cn(
                        "flex items-center space-x-2 text-blue-600 dark:text-blue-400",
                        isPulling && "animate-pulse"
                    )}>
                        <div className={cn(
                            "w-6 h-6 border-2 border-blue-600 dark:border-blue-400 rounded-full",
                            isPulling ? "animate-spin border-t-transparent" : ""
                        )} />
                        <span className="text-sm font-medium">
                            {isPulling ? "Release to refresh" : "Pull to refresh"}
                        </span>
                    </div>
                </div>
            )}
            
            <div style={{ transform: `translateY(${pullDistance}px)` }}>
                {children}
            </div>
        </div>
    );
}
