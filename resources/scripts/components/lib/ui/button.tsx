import { cn } from "@/utils";
import { PropsWithChildren } from "react";
import { SpinnerLoader } from "./loader";

type Props = PropsWithChildren<{
    variant?:
        | "primary"
        | "outline"
        | "outline-dark"
        | "outline-danger"
        | "ghost"
        | "light"
        | "dark"
        | "grey"
        | "danger";
    loading?: boolean;
    shape?: "circle";
    spinnerLoaderSize?: number;
    replaceValueOnLoading?: boolean;
}> &
    React.ComponentPropsWithRef<"button">;

export function Button(props: Props) {
    const {
        children,
        className,
        variant,
        loading,
        replaceValueOnLoading,
        spinnerLoaderSize = 17,
        disabled,
        shape,
        ...rest
    } = props;

    return (
        <button
            type="button"
            disabled={disabled || loading}
            className={cn(
                "p-1 rounded-md inline-flex flex-row items-center transition-all",
                "disabled:opacity-80",
                "transition-all gap-2",

                variant && ["p-2 rounded-md text-sm font-medium"],

                variant === "primary" && [
                    "text-white",
                    "bg-primary hover:bg-primary-600",
                ],

                variant === "light" && [
                    "bg-light-primary-background hover:bg-light-primary-700 p-2",
                    "text-primary-600",
                    "border border-gray-200 dark:border-gray-600",
                ],

                variant === "outline" && [
                    "text-primary-600 dark:text-primary-400",
                    "border border-primary-600 hover:bg-primary-50 dark:hover:bg-primary-700",
                ],

                shape === "circle" && ["rounded-full"],

                className
            )}
            {...rest}
        >
            {loading && (
                <SpinnerLoader
                    size={spinnerLoaderSize}
                    variant={variant !== "primary" ? "primary" : "light"}
                    forcePrimary={variant == "light"}
                />
            )}
            {replaceValueOnLoading && loading ? undefined : children}
        </button>
    );
}
