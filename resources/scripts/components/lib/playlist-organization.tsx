import { useState } from "react";
import { useModal, useZiggy } from "@/hooks";
import { Button, InputField, Modal, ModalBody } from "./ui";
import { fetchApi } from "@/utils";
import { router } from "@inertiajs/react";
import { 
    Io<PERSON>older, 
    IoAdd, 
    IoCreate,
    IoTrash,
    IoColorPalette,
    IoTag
} from "./icons";

interface PlaylistOrganizationModalProps {
    isOpen: boolean;
    onClose: () => void;
    playlist?: any;
    folders?: any[];
}

export function PlaylistOrganizationModal({ 
    isOpen, 
    onClose, 
    playlist,
    folders = []
}: PlaylistOrganizationModalProps) {
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState<"folders" | "tags">("folders");
    const [newFolderName, setNewFolderName] = useState("");
    const [newFolderColor, setNewFolderColor] = useState("#3B82F6");
    const [newTag, setNewTag] = useState("");
    const [playlistTags, setPlaylistTags] = useState<string[]>(playlist?.tags || []);
    const route = useZiggy();

    const handleCreateFolder = async () => {
        if (!newFolderName.trim()) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlist-folders.create"), {
                method: "POST",
                body: JSON.stringify({ 
                    name: newFolderName.trim(),
                    color: newFolderColor
                }),
            });

            setNewFolderName("");
            setNewFolderColor("#3B82F6");
            router.reload();
        } catch (error) {
            console.error("Failed to create folder:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleMoveToFolder = async (folderId: string | null) => {
        if (!playlist) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlists.move-to-folder", { playlist: playlist.id }), {
                method: "PUT",
                body: JSON.stringify({ folder_id: folderId }),
            });

            router.reload();
            onClose();
        } catch (error) {
            console.error("Failed to move playlist:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleAddTag = () => {
        if (!newTag.trim() || playlistTags.includes(newTag.trim())) return;
        
        const updatedTags = [...playlistTags, newTag.trim()];
        setPlaylistTags(updatedTags);
        setNewTag("");
    };

    const handleRemoveTag = (tagToRemove: string) => {
        setPlaylistTags(playlistTags.filter(tag => tag !== tagToRemove));
    };

    const handleSaveTags = async () => {
        if (!playlist) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlists.update-tags", { playlist: playlist.id }), {
                method: "PUT",
                body: JSON.stringify({ tags: playlistTags }),
            });

            router.reload();
            onClose();
        } catch (error) {
            console.error("Failed to update tags:", error);
        } finally {
            setLoading(false);
        }
    };

    const colorOptions = [
        "#3B82F6", "#EF4444", "#10B981", "#F59E0B", 
        "#8B5CF6", "#EC4899", "#06B6D4", "#84CC16"
    ];

    return (
        <Modal isOpen={isOpen} onClose={onClose} size="lg">
            <ModalBody>
                <div className="space-y-6">
                    <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Organize Playlist
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            {playlist ? `Organize "${playlist.name}"` : "Manage your playlist organization"}
                        </p>
                    </div>

                    {/* Tabs */}
                    <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                        <button
                            onClick={() => setActiveTab("folders")}
                            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                                activeTab === "folders"
                                    ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm"
                                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                            }`}
                        >
                            <IoFolder className="h-4 w-4 inline mr-2" />
                            Folders
                        </button>
                        <button
                            onClick={() => setActiveTab("tags")}
                            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                                activeTab === "tags"
                                    ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm"
                                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                            }`}
                        >
                            <IoTag className="h-4 w-4 inline mr-2" />
                            Tags
                        </button>
                    </div>

                    {/* Folders Tab */}
                    {activeTab === "folders" && (
                        <div className="space-y-4">
                            {/* Create New Folder */}
                            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                                    Create New Folder
                                </h4>
                                <div className="flex space-x-3">
                                    <InputField
                                        placeholder="Folder name"
                                        value={newFolderName}
                                        onChange={(e) => setNewFolderName(e.target.value)}
                                        className="flex-1"
                                    />
                                    <div className="flex space-x-1">
                                        {colorOptions.map((color) => (
                                            <button
                                                key={color}
                                                onClick={() => setNewFolderColor(color)}
                                                className={`w-8 h-8 rounded-full border-2 ${
                                                    newFolderColor === color 
                                                        ? "border-gray-900 dark:border-gray-100" 
                                                        : "border-gray-300 dark:border-gray-600"
                                                }`}
                                                style={{ backgroundColor: color }}
                                            />
                                        ))}
                                    </div>
                                    <Button
                                        onClick={handleCreateFolder}
                                        disabled={loading || !newFolderName.trim()}
                                        variant="primary"
                                        size="sm"
                                    >
                                        <IoAdd className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>

                            {/* Move to Folder */}
                            {playlist && (
                                <div>
                                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                                        Move to Folder
                                    </h4>
                                    <div className="space-y-2">
                                        <button
                                            onClick={() => handleMoveToFolder(null)}
                                            disabled={loading}
                                            className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                                        >
                                            <div className="flex items-center space-x-3">
                                                <IoFolder className="h-5 w-5 text-gray-400" />
                                                <span className="text-gray-900 dark:text-gray-100">No Folder</span>
                                            </div>
                                        </button>
                                        {folders.map((folder) => (
                                            <button
                                                key={folder.id}
                                                onClick={() => handleMoveToFolder(folder.id)}
                                                disabled={loading}
                                                className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                                            >
                                                <div className="flex items-center space-x-3">
                                                    <IoFolder 
                                                        className="h-5 w-5" 
                                                        style={{ color: folder.color || "#3B82F6" }}
                                                    />
                                                    <span className="text-gray-900 dark:text-gray-100">{folder.name}</span>
                                                </div>
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {/* Tags Tab */}
                    {activeTab === "tags" && playlist && (
                        <div className="space-y-4">
                            {/* Add New Tag */}
                            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                                    Add Tags
                                </h4>
                                <div className="flex space-x-3">
                                    <InputField
                                        placeholder="Enter tag"
                                        value={newTag}
                                        onChange={(e) => setNewTag(e.target.value)}
                                        onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
                                        className="flex-1"
                                    />
                                    <Button
                                        onClick={handleAddTag}
                                        disabled={!newTag.trim() || playlistTags.includes(newTag.trim())}
                                        variant="primary"
                                        size="sm"
                                    >
                                        <IoAdd className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>

                            {/* Current Tags */}
                            <div>
                                <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                                    Current Tags
                                </h4>
                                {playlistTags.length === 0 ? (
                                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                                        No tags added yet
                                    </p>
                                ) : (
                                    <div className="flex flex-wrap gap-2">
                                        {playlistTags.map((tag) => (
                                            <span
                                                key={tag}
                                                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                                            >
                                                {tag}
                                                <button
                                                    onClick={() => handleRemoveTag(tag)}
                                                    className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                                                >
                                                    ×
                                                </button>
                                            </span>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    <div className="flex justify-end space-x-3 pt-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                        >
                            Cancel
                        </Button>
                        {activeTab === "tags" && playlist && (
                            <Button
                                onClick={handleSaveTags}
                                disabled={loading}
                                variant="primary"
                            >
                                Save Tags
                            </Button>
                        )}
                    </div>
                </div>
            </ModalBody>
        </Modal>
    );
}

export function usePlaylistOrganization() {
    const organizationModal = useModal();

    return {
        organizationModal,
        PlaylistOrganizationModal,
    };
}
