import { useState } from "react";
import { useModal, useZiggy } from "@/hooks";
import { <PERSON><PERSON>, Modal, ModalBody } from "./ui";
import { fetchApi } from "@/utils";
import { router } from "@inertiajs/react";
import { 
    IoCheckbox, 
    IoSquareOutline,
    IoAdd,
    IoTrash,
    IoFolder,
    IoDownload,
    IoCopy
} from "./icons";

interface PlaylistBulkOperationsProps {
    playlists: any[];
    onSelectionChange?: (selectedIds: string[]) => void;
}

export function PlaylistBulkOperations({ 
    playlists,
    onSelectionChange 
}: PlaylistBulkOperationsProps) {
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    const route = useZiggy();
    const bulkModal = useModal();

    const handleSelectAll = () => {
        const allIds = playlists.map(p => p.id);
        const newSelection = selectedIds.length === allIds.length ? [] : allIds;
        setSelectedIds(newSelection);
        onSelectionChange?.(newSelection);
    };

    const handleSelectPlaylist = (playlistId: string) => {
        const newSelection = selectedIds.includes(playlistId)
            ? selectedIds.filter(id => id !== playlistId)
            : [...selectedIds, playlistId];
        setSelectedIds(newSelection);
        onSelectionChange?.(newSelection);
    };

    const handleBulkDelete = async () => {
        if (selectedIds.length === 0) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlists.bulk-delete"), {
                method: "DELETE",
                body: JSON.stringify({ playlist_ids: selectedIds }),
            });

            setSelectedIds([]);
            router.reload();
            bulkModal.closeModal();
        } catch (error) {
            console.error("Failed to delete playlists:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleBulkAddToFolder = async (folderId: string) => {
        if (selectedIds.length === 0) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlists.bulk-move-to-folder"), {
                method: "PUT",
                body: JSON.stringify({ 
                    playlist_ids: selectedIds,
                    folder_id: folderId 
                }),
            });

            setSelectedIds([]);
            router.reload();
        } catch (error) {
            console.error("Failed to move playlists:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleBulkDownload = async () => {
        if (selectedIds.length === 0) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlists.bulk-download"), {
                method: "POST",
                body: JSON.stringify({ playlist_ids: selectedIds }),
            });

            // Handle download response
        } catch (error) {
            console.error("Failed to download playlists:", error);
        } finally {
            setLoading(false);
        }
    };

    const isAllSelected = selectedIds.length === playlists.length && playlists.length > 0;
    const isSomeSelected = selectedIds.length > 0 && selectedIds.length < playlists.length;

    return (
        <>
            {/* Selection Controls */}
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                    <button
                        onClick={handleSelectAll}
                        className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                    >
                        {isAllSelected ? (
                            <IoCheckbox className="h-5 w-5 text-blue-600" />
                        ) : isSomeSelected ? (
                            <div className="h-5 w-5 bg-blue-600 rounded border-2 border-blue-600 flex items-center justify-center">
                                <div className="h-2 w-2 bg-white rounded-sm" />
                            </div>
                        ) : (
                            <IoSquareOutline className="h-5 w-5" />
                        )}
                        <span>
                            {isAllSelected ? "Deselect All" : "Select All"}
                        </span>
                    </button>
                    
                    {selectedIds.length > 0 && (
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                            {selectedIds.length} selected
                        </span>
                    )}
                </div>

                {/* Bulk Actions */}
                {selectedIds.length > 0 && (
                    <div className="flex items-center space-x-2">
                        <Button
                            onClick={bulkModal.openModal}
                            variant="outline"
                            size="sm"
                            className="flex items-center space-x-2"
                        >
                            <IoFolder className="h-4 w-4" />
                            <span>Move to Folder</span>
                        </Button>
                        
                        <Button
                            onClick={handleBulkDownload}
                            disabled={loading}
                            variant="outline"
                            size="sm"
                            className="flex items-center space-x-2"
                        >
                            <IoDownload className="h-4 w-4" />
                            <span>Download</span>
                        </Button>

                        <Button
                            onClick={handleBulkDelete}
                            disabled={loading}
                            variant="outline"
                            size="sm"
                            className="flex items-center space-x-2 text-red-600 hover:text-red-700"
                        >
                            <IoTrash className="h-4 w-4" />
                            <span>Delete</span>
                        </Button>
                    </div>
                )}
            </div>

            {/* Playlist Grid with Selection */}
            <div className="space-y-2">
                {playlists.map((playlist) => (
                    <div
                        key={playlist.id}
                        className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                            selectedIds.includes(playlist.id)
                                ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                                : "border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
                        }`}
                    >
                        <button
                            onClick={() => handleSelectPlaylist(playlist.id)}
                            className="flex-shrink-0"
                        >
                            {selectedIds.includes(playlist.id) ? (
                                <IoCheckbox className="h-5 w-5 text-blue-600" />
                            ) : (
                                <IoSquareOutline className="h-5 w-5 text-gray-400" />
                            )}
                        </button>
                        
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-3">
                                <img
                                    src={playlist.image?.url || "/placeholder-playlist.png"}
                                    alt={playlist.name}
                                    className="w-12 h-12 rounded-lg object-cover"
                                />
                                <div className="flex-1 min-w-0">
                                    <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                                        {playlist.name}
                                    </h3>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                        {playlist.articles_count || 0} songs
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Bulk Actions Modal */}
            <Modal isOpen={bulkModal.isOpen} onClose={bulkModal.closeModal}>
                <ModalBody>
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                            Move {selectedIds.length} playlist{selectedIds.length !== 1 ? 's' : ''} to folder
                        </h3>
                        
                        <div className="space-y-2">
                            <button
                                onClick={() => handleBulkAddToFolder("")}
                                disabled={loading}
                                className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                            >
                                <div className="flex items-center space-x-3">
                                    <IoFolder className="h-5 w-5 text-gray-400" />
                                    <span className="text-gray-900 dark:text-gray-100">No Folder</span>
                                </div>
                            </button>
                            {/* Add folder options here */}
                        </div>

                        <div className="flex justify-end space-x-3 pt-4">
                            <Button
                                onClick={bulkModal.closeModal}
                                variant="outline"
                            >
                                Cancel
                            </Button>
                        </div>
                    </div>
                </ModalBody>
            </Modal>
        </>
    );
}

export function usePlaylistBulkOperations() {
    return {
        PlaylistBulkOperations,
    };
}
