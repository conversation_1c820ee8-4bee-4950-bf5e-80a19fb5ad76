import { useState } from "react";
import { But<PERSON> } from "./ui";
import { Io<PERSON>ersonAdd, IoPersonRemove, IoCheckmark } from "./icons";
import { fetchApi, cn } from "@/utils";
import { useZiggy } from "@/hooks";

interface FollowButtonProps {
    userId: string;
    initialFollowing?: boolean;
    initialFollowersCount?: number;
    variant?: "primary" | "outline" | "ghost";
    size?: "sm" | "md" | "lg";
    showCount?: boolean;
    className?: string;
    onFollowChange?: (isFollowing: boolean, followersCount: number) => void;
}

export function FollowButton({
    userId,
    initialFollowing = false,
    initialFollowersCount = 0,
    variant = "primary",
    size = "md",
    showCount = false,
    className,
    onFollowChange,
}: FollowButtonProps) {
    const [isFollowing, setIsFollowing] = useState(initialFollowing);
    const [followersCount, setFollowersCount] = useState(initialFollowersCount);
    const [loading, setLoading] = useState(false);
    const [hovered, setHovered] = useState(false);
    const route = useZiggy();

    const handleToggleFollow = async () => {
        setLoading(true);
        try {
            const response = await fetchApi(route("app.social.follow", { user: userId }), {
                method: "POST",
            });

            setIsFollowing(response.is_following);
            setFollowersCount(response.followers_count);
            
            if (onFollowChange) {
                onFollowChange(response.is_following, response.followers_count);
            }
        } catch (error) {
            console.error("Failed to toggle follow:", error);
        } finally {
            setLoading(false);
        }
    };

    const getButtonText = () => {
        if (loading) return "...";
        
        if (isFollowing) {
            return hovered ? "Unfollow" : "Following";
        }
        
        return "Follow";
    };

    const getButtonIcon = () => {
        if (loading) return null;
        
        if (isFollowing) {
            return hovered ? (
                <IoPersonRemove className="h-4 w-4" />
            ) : (
                <IoCheckmark className="h-4 w-4" />
            );
        }
        
        return <IoPersonAdd className="h-4 w-4" />;
    };

    const getButtonVariant = () => {
        if (isFollowing && hovered) {
            return "danger";
        }
        
        if (isFollowing) {
            return "outline";
        }
        
        return variant;
    };

    return (
        <div className={cn("flex items-center space-x-2", className)}>
            <Button
                variant={getButtonVariant()}
                size={size}
                onClick={handleToggleFollow}
                disabled={loading}
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
                className={cn(
                    "flex items-center space-x-2 transition-all duration-200",
                    isFollowing && hovered && "border-red-500 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                )}
            >
                {getButtonIcon()}
                <span>{getButtonText()}</span>
            </Button>
            
            {showCount && (
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    {followersCount.toLocaleString()} {followersCount === 1 ? "follower" : "followers"}
                </span>
            )}
        </div>
    );
}

// Compact version for smaller spaces
export function CompactFollowButton({
    userId,
    initialFollowing = false,
    className,
    onFollowChange,
}: Pick<FollowButtonProps, "userId" | "initialFollowing" | "className" | "onFollowChange">) {
    const [isFollowing, setIsFollowing] = useState(initialFollowing);
    const [loading, setLoading] = useState(false);
    const route = useZiggy();

    const handleToggleFollow = async () => {
        setLoading(true);
        try {
            const response = await fetchApi(route("app.social.follow", { user: userId }), {
                method: "POST",
            });

            setIsFollowing(response.is_following);
            
            if (onFollowChange) {
                onFollowChange(response.is_following, response.followers_count);
            }
        } catch (error) {
            console.error("Failed to toggle follow:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <button
            onClick={handleToggleFollow}
            disabled={loading}
            className={cn(
                "p-2 rounded-full transition-colors",
                isFollowing
                    ? "bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30"
                    : "bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30",
                loading && "opacity-50 cursor-not-allowed",
                className
            )}
            title={isFollowing ? "Unfollow" : "Follow"}
        >
            {loading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : isFollowing ? (
                <IoCheckmark className="h-4 w-4" />
            ) : (
                <IoPersonAdd className="h-4 w-4" />
            )}
        </button>
    );
}

// Follow stats component
export function FollowStats({
    followersCount,
    followingCount,
    userId,
    className,
}: {
    followersCount: number;
    followingCount: number;
    userId: string;
    className?: string;
}) {
    const route = useZiggy();

    return (
        <div className={cn("flex items-center space-x-6", className)}>
            <button
                onClick={() => {
                    // Open followers modal or navigate to followers page
                    window.open(route("app.social.followers", { user: userId }), "_blank");
                }}
                className="text-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
                <div className="font-bold text-lg">{followersCount.toLocaleString()}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                    {followersCount === 1 ? "Follower" : "Followers"}
                </div>
            </button>
            
            <button
                onClick={() => {
                    // Open following modal or navigate to following page
                    window.open(route("app.social.following", { user: userId }), "_blank");
                }}
                className="text-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
                <div className="font-bold text-lg">{followingCount.toLocaleString()}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Following</div>
            </button>
        </div>
    );
}

// Social share button
export function ShareButton({
    type,
    id,
    title,
    className,
}: {
    type: "article" | "playlist" | "channel";
    id: string;
    title: string;
    className?: string;
}) {
    const [loading, setLoading] = useState(false);
    const route = useZiggy();

    const handleShare = async () => {
        // Try native sharing first
        if (navigator.share) {
            try {
                await navigator.share({
                    title: title,
                    text: `Check out this ${type}: ${title}`,
                    url: window.location.href,
                });
                return;
            } catch (error) {
                // Fall back to API sharing
            }
        }

        // API sharing
        setLoading(true);
        try {
            await fetchApi(route("app.social.share"), {
                method: "POST",
                body: JSON.stringify({
                    type,
                    id,
                    message: `Check out this ${type}: ${title}`,
                }),
            });
            
            // Show success message or copy to clipboard
            await navigator.clipboard.writeText(window.location.href);
            alert("Link copied to clipboard!");
        } catch (error) {
            console.error("Failed to share:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <button
            onClick={handleShare}
            disabled={loading}
            className={cn(
                "p-2 rounded-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",
                loading && "opacity-50 cursor-not-allowed",
                className
            )}
            title="Share"
        >
            {loading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
                <IoShare className="h-4 w-4" />
            )}
        </button>
    );
}
