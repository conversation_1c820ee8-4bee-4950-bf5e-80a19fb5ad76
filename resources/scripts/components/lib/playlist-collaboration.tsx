import { useState } from "react";
import { useModal, useZiggy } from "@/hooks";
import { Button, InputField, Modal, ModalBody } from "./ui";
import { fetchApi } from "@/utils";
import { router } from "@inertiajs/react";
import { 
    IoPersonAdd, 
    IoCheckmark, 
    IoClose, 
    IoTrash,
    IoSettings
} from "./icons";

interface PlaylistCollaborationModalProps {
    isOpen: boolean;
    onClose: () => void;
    playlist: any;
    collaborators?: any[];
}

export function PlaylistCollaborationModal({ 
    isOpen, 
    onClose, 
    playlist,
    collaborators = []
}: PlaylistCollaborationModalProps) {
    const [loading, setLoading] = useState(false);
    const [email, setEmail] = useState("");
    const [permission, setPermission] = useState("edit");
    const route = useZiggy();

    const handleInviteCollaborator = async () => {
        if (!email.trim()) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlists.invite-collaborator", { playlist: playlist.id }), {
                method: "POST",
                body: JSON.stringify({ 
                    email: email.trim(),
                    permission 
                }),
            });

            setEmail("");
            router.reload();
        } catch (error) {
            console.error("Failed to invite collaborator:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleRemoveCollaborator = async (collaboratorId: string) => {
        setLoading(true);
        try {
            await fetchApi(route("app.playlists.remove-collaborator", { 
                playlist: playlist.id,
                collaborator: collaboratorId 
            }), {
                method: "DELETE",
            });

            router.reload();
        } catch (error) {
            console.error("Failed to remove collaborator:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleUpdatePermission = async (collaboratorId: string, newPermission: string) => {
        setLoading(true);
        try {
            await fetchApi(route("app.playlists.update-collaborator", { 
                playlist: playlist.id,
                collaborator: collaboratorId 
            }), {
                method: "PUT",
                body: JSON.stringify({ permission: newPermission }),
            });

            router.reload();
        } catch (error) {
            console.error("Failed to update permission:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} size="lg">
            <ModalBody>
                <div className="space-y-6">
                    <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Collaborate on "{playlist.name}"
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            Invite others to help you manage this playlist
                        </p>
                    </div>

                    {/* Invite New Collaborator */}
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                            Invite Collaborator
                        </h4>
                        <div className="flex space-x-3">
                            <InputField
                                type="email"
                                placeholder="Enter email address"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className="flex-1"
                            />
                            <select
                                value={permission}
                                onChange={(e) => setPermission(e.target.value)}
                                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                            >
                                <option value="view">View Only</option>
                                <option value="edit">Can Edit</option>
                                <option value="admin">Admin</option>
                            </select>
                            <Button
                                onClick={handleInviteCollaborator}
                                disabled={loading || !email.trim()}
                                variant="primary"
                                className="flex items-center space-x-2"
                            >
                                <IoPersonAdd className="h-4 w-4" />
                                <span>Invite</span>
                            </Button>
                        </div>
                    </div>

                    {/* Current Collaborators */}
                    <div>
                        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                            Current Collaborators ({collaborators.length})
                        </h4>
                        {collaborators.length === 0 ? (
                            <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                                No collaborators yet. Invite someone to get started!
                            </p>
                        ) : (
                            <div className="space-y-3">
                                {collaborators.map((collaborator) => (
                                    <div 
                                        key={collaborator.id}
                                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                                    >
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                                {collaborator.user?.name?.charAt(0).toUpperCase()}
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {collaborator.user?.name}
                                                </p>
                                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                                    {collaborator.user?.email}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <select
                                                value={collaborator.permission}
                                                onChange={(e) => handleUpdatePermission(collaborator.id, e.target.value)}
                                                disabled={loading}
                                                className="text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                            >
                                                <option value="view">View</option>
                                                <option value="edit">Edit</option>
                                                <option value="admin">Admin</option>
                                            </select>
                                            <Button
                                                onClick={() => handleRemoveCollaborator(collaborator.id)}
                                                disabled={loading}
                                                variant="outline"
                                                size="sm"
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <IoTrash className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                        >
                            Done
                        </Button>
                    </div>
                </div>
            </ModalBody>
        </Modal>
    );
}

export function usePlaylistCollaboration() {
    const collaborationModal = useModal();

    return {
        collaborationModal,
        PlaylistCollaborationModal,
    };
}
