export {
    IoGridOutline,
    IoSearchOutline,
    IoDiscOutline,
    IoAlbumsOutline,
    IoHeartOutline,
    IoSunnyOutline,
    IoSunny,
    IoMoon,
    IoStatsChart,
    IoPersonCircle,
    IoLogOutOutline,
    IoCloseCircleOutline,
    IoLogInOutline,
    IoLogIn,
    IoDocument,
    IoCall,
    IoArrowForwardCircleOutline,
    IoArrowForwardSharp,
    IoArrowBack,
    IoClose,
    IoInformationSharp,
    IoCheckmark,
    IoChevronDown,
    IoShareSocial,
    IoWarning,
    IoInformationCircle,
    IoLockClosed,
    IoMusicalNotes,
    IoPencil,
    IoMicCircleOutline,
    IoGrid,
    IoMicOutline,
    IoPersonOutline,
    IoMusicalNoteSharp,
    IoSearch,
    IoDisc,
    IoAlbums,
    IoMicCircle,
    IoHeart,
    IoPlaySharp,
    IoSettingsSharp,
    IoVolumeMedium,
    IoPlaySkipForwardSharp,
    IoPlaySkipBackSharp,
    IoPause,
    IoPauseSharp,
    IoPlay,
    IoVolumeMute,
    IoVolumeOff,
    IoShuffle,
    IoRadioOutline,
    IoChatboxEllipses,
    IoChatboxEllipsesOutline,
    IoTrashOutline,
    IoDownloadOutline,
    IoCloudDownloadOutline,
    IoTrophyOutline,
    IoTrophy,
    IoLibraryOutline,
    IoLibrary,
    IoAdd,
    IoEllipsisVertical,
    IoTrash,
    IoCreate,
    IoChatbubbleOutline,
    IoChatbubble,
    IoPersonAdd,
    IoCloudDownload,
    IoCloudOffline,
    IoWifi,
    IoRefresh,
    IoPeopleOutline,
    IoPeople,
    IoShare,
    IoTrendingUp,
} from "react-icons/io5";

export { HiBell } from "react-icons/hi";

export { SiDjango } from "react-icons/si";

export {
    LuVerified,
    LuRepeat,
    LuRepeat1,
    LuRepeat2,
    LuShuffle,
} from "react-icons/lu";

export function BiSolidPlaylist({ className }: { className?: string }) {
    return (
        <svg
            stroke="currentColor"
            fill="currentColor"
            strokeWidth="0"
            viewBox="0 0 24 24"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
        >
            <path d="M13 16.493C13 18.427 14.573 20 16.507 20s3.507-1.573 3.507-3.507c0-.177-.027-.347-.053-.517H20V6h2V4h-3a1 1 0 0 0-1 1v8.333a3.465 3.465 0 0 0-1.493-.346A3.51 3.51 0 0 0 13 16.493zM2 5h14v2H2z"></path>
            <path d="M2 9h14v2H2zm0 4h9v2H2zm0 4h9v2H2z"></path>
        </svg>
    );
}
