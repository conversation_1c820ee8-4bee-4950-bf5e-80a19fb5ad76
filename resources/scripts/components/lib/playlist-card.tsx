import { useState } from "react";
import { Link } from "@inertiajs/react";
import { CardImage } from "./ui";
import { useZiggy, useItemLike } from "@/hooks";
import { asset, cn } from "@/utils";
import {
    IoPlay,
    IoPause,
    IoHeart,
    IoHeartOutline,
    IoEllipsisVertical,
    IoTrash,
    IoCreate,
} from "./icons";
import { usePlayer } from "../player";
import { Menu, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { fetchApi } from "@/utils";
import { router } from "@inertiajs/react";

interface PlaylistCardProps {
    playlist: any;
    showActions?: boolean;
    className?: string;
}

export function PlaylistCard({
    playlist,
    showActions = false,
    className,
}: PlaylistCardProps) {
    const route = useZiggy();
    const player = usePlayer();
    const { likeCall } = useItemLike();
    const [loading, setLoading] = useState(false);

    const image = playlist.image || playlist.fallback_image;
    const playlistLink = route("app.playlists.show", { playlist: playlist.id });

    // Check if this playlist is currently playing
    const isCurrentPlaylist = player?.playlistId === playlist.id;
    const isPlaying = isCurrentPlaylist && player?.isPlaying;

    const handlePlayClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        if (isCurrentPlaylist) {
            if (isPlaying) {
                player?.actions.pause();
            } else {
                player?.actions.play();
            }
        } else {
            // Load and play playlist
            player?.guessPlaylist({
                type: "playlist",
                data: playlist,
            });
        }
    };

    const handleLikeClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        likeCall({ type: "playlist", data: playlist }, true);
    };

    const handleDeletePlaylist = async () => {
        if (!confirm("Are you sure you want to delete this playlist?")) {
            return;
        }

        setLoading(true);
        try {
            await fetchApi(
                route("app.playlists.destroy", { playlist: playlist.id }),
                {
                    method: "DELETE",
                }
            );
            router.reload();
        } catch (error) {
            console.error("Failed to delete playlist:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className={cn("group relative", className)}>
            <Link href={playlistLink} className="block">
                <CardImage
                    picture={asset(image) || ""}
                    alt={playlist.name}
                    height="h-40"
                    width="w-full"
                />

                {/* Play button overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 rounded-lg flex items-center justify-center">
                    <button
                        onClick={handlePlayClick}
                        className="opacity-0 group-hover:opacity-100 duration-200 bg-primary text-white rounded-full p-3 hover:scale-110 transform transition-all"
                    >
                        {isPlaying ? (
                            <IoPause className="h-6 w-6" />
                        ) : (
                            <IoPlay className="h-6 w-6 ml-1" />
                        )}
                    </button>
                </div>
            </Link>

            {/* Playlist info */}
            <div className="mt-3 space-y-1">
                <Link href={playlistLink}>
                    <h3 className="font-medium text-sm line-clamp-2 hover:underline">
                        {playlist.name}
                    </h3>
                </Link>

                <div className="flex items-center justify-between">
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                        By {playlist.user?.name}
                    </p>

                    <div className="flex items-center space-x-2">
                        {/* Like button */}
                        <button
                            onClick={handleLikeClick}
                            className={cn(
                                "p-1 rounded-full transition-colors",
                                playlist.liked
                                    ? "text-red-500 hover:text-red-600"
                                    : "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            )}
                        >
                            {playlist.liked ? (
                                <IoHeart className="h-4 w-4" />
                            ) : (
                                <IoHeartOutline className="h-4 w-4" />
                            )}
                        </button>

                        {/* Actions menu for user's own playlists */}
                        {showActions && !playlist.is_system_generated && (
                            <Menu as="div" className="relative">
                                <Menu.Button className="p-1 rounded-full text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                                    <IoEllipsisVertical className="h-4 w-4" />
                                </Menu.Button>

                                <Transition
                                    as={Fragment}
                                    enter="transition ease-out duration-100"
                                    enterFrom="transform opacity-0 scale-95"
                                    enterTo="transform opacity-100 scale-100"
                                    leave="transition ease-in duration-75"
                                    leaveFrom="transform opacity-100 scale-100"
                                    leaveTo="transform opacity-0 scale-95"
                                >
                                    <Menu.Items className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                                        <div className="py-1">
                                            <Menu.Item>
                                                {({ active }) => (
                                                    <Link
                                                        href={`${playlistLink}/edit`}
                                                        className={cn(
                                                            "flex items-center px-4 py-2 text-sm",
                                                            active
                                                                ? "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                                                : "text-gray-700 dark:text-gray-300"
                                                        )}
                                                    >
                                                        <IoCreate className="h-4 w-4 mr-3" />
                                                        Edit Playlist
                                                    </Link>
                                                )}
                                            </Menu.Item>

                                            <Menu.Item>
                                                {({ active }) => (
                                                    <button
                                                        onClick={
                                                            handleDeletePlaylist
                                                        }
                                                        disabled={loading}
                                                        className={cn(
                                                            "flex items-center w-full px-4 py-2 text-sm",
                                                            active
                                                                ? "bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400"
                                                                : "text-red-600 dark:text-red-400"
                                                        )}
                                                    >
                                                        <IoTrash className="h-4 w-4 mr-3" />
                                                        {loading
                                                            ? "Deleting..."
                                                            : "Delete Playlist"}
                                                    </button>
                                                )}
                                            </Menu.Item>
                                        </div>
                                    </Menu.Items>
                                </Transition>
                            </Menu>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
