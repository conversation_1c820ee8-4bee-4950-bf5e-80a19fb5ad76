import { useState } from "react";
import { use<PERSON>tom, useAtomValue } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { <PERSON><PERSON>, <PERSON>dal, ModalBody } from "@/components/lib/ui";
import { 
    IoSettings, 
    IoMusicalNotes, 
    IoSpeedometer,
    IoVolumeHigh,
    IoCheckmark
} from "@/components/lib/icons";
import { cn } from "@/utils";
import { useModal } from "@/hooks";

// Audio settings atoms
export const audioSettingsAtom = atomWithStorage("audio-settings", {
    crossfadeDuration: 3, // seconds
    gaplessPlayback: true,
    audioQuality: "high" as "low" | "medium" | "high",
    normalizeVolume: false,
    replayGain: false,
    preloadNext: true,
    fadeInOut: true,
});

export const equalizerAtom = atomWithStorage("equalizer-settings", {
    enabled: false,
    preset: "flat" as "flat" | "rock" | "pop" | "jazz" | "classical" | "electronic" | "custom",
    bands: {
        "60": 0,    // 60Hz
        "170": 0,   // 170Hz
        "310": 0,   // 310Hz
        "600": 0,   // 600Hz
        "1000": 0,  // 1kHz
        "3000": 0,  // 3kHz
        "6000": 0,  // 6kHz
        "12000": 0, // 12kHz
        "14000": 0, // 14kHz
        "16000": 0, // 16kHz
    }
});

const EQ_PRESETS = {
    flat: { "60": 0, "170": 0, "310": 0, "600": 0, "1000": 0, "3000": 0, "6000": 0, "12000": 0, "14000": 0, "16000": 0 },
    rock: { "60": 4, "170": 3, "310": -1, "600": -2, "1000": 1, "3000": 3, "6000": 4, "12000": 3, "14000": 2, "16000": 1 },
    pop: { "60": -1, "170": 2, "310": 3, "600": 2, "1000": 0, "3000": -1, "6000": -1, "12000": 1, "14000": 2, "16000": 2 },
    jazz: { "60": 2, "170": 1, "310": 0, "600": 1, "1000": 2, "3000": 2, "6000": 1, "12000": 0, "14000": 1, "16000": 2 },
    classical: { "60": 3, "170": 2, "310": -1, "600": -1, "1000": 0, "3000": 1, "6000": 2, "12000": 3, "14000": 2, "16000": 1 },
    electronic: { "60": 3, "170": 2, "310": 0, "600": -1, "1000": 1, "3000": 0, "6000": 2, "12000": 3, "14000": 4, "16000": 3 },
};

export function AudioSettingsButton() {
    const modal = useModal();

    return (
        <>
            <button
                onClick={modal.openModal}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                title="Audio Settings"
            >
                <IoSettings className="h-5 w-5" />
            </button>

            <AudioSettingsModal
                isOpen={modal.isOpen}
                onClose={modal.closeModal}
            />
        </>
    );
}

interface AudioSettingsModalProps {
    isOpen: boolean;
    onClose: () => void;
}

function AudioSettingsModal({ isOpen, onClose }: AudioSettingsModalProps) {
    const [activeTab, setActiveTab] = useState<"general" | "equalizer">("general");
    const [audioSettings, setAudioSettings] = useAtom(audioSettingsAtom);
    const [eqSettings, setEqSettings] = useAtom(equalizerAtom);

    const handleSettingChange = (key: keyof typeof audioSettings, value: any) => {
        setAudioSettings(prev => ({ ...prev, [key]: value }));
    };

    const handleEqChange = (key: keyof typeof eqSettings, value: any) => {
        setEqSettings(prev => ({ ...prev, [key]: value }));
    };

    const handleEqBandChange = (frequency: string, value: number) => {
        setEqSettings(prev => ({
            ...prev,
            bands: { ...prev.bands, [frequency]: value }
        }));
    };

    const applyEqPreset = (preset: keyof typeof EQ_PRESETS) => {
        setEqSettings(prev => ({
            ...prev,
            preset,
            bands: EQ_PRESETS[preset]
        }));
    };

    const tabs = [
        { id: "general", label: "General", icon: IoMusicalNotes },
        { id: "equalizer", label: "Equalizer", icon: IoSpeedometer },
    ];

    return (
        <Modal isOpen={isOpen} closeModal={onClose}>
            <ModalBody onClose={onClose} className="max-w-2xl">
                <h2 className="text-xl font-semibold mb-6">Audio Settings</h2>

                {/* Tabs */}
                <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                    {tabs.map((tab) => (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id as any)}
                            className={cn(
                                "flex items-center space-x-2 px-4 py-2 rounded-md transition-colors flex-1 justify-center",
                                activeTab === tab.id
                                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm"
                                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                            )}
                        >
                            <tab.icon className="h-4 w-4" />
                            <span>{tab.label}</span>
                        </button>
                    ))}
                </div>

                {/* General Settings */}
                {activeTab === "general" && (
                    <div className="space-y-6">
                        {/* Crossfade */}
                        <div>
                            <label className="block text-sm font-medium mb-2">
                                Crossfade Duration: {audioSettings.crossfadeDuration}s
                            </label>
                            <input
                                type="range"
                                min="0"
                                max="10"
                                step="0.5"
                                value={audioSettings.crossfadeDuration}
                                onChange={(e) => handleSettingChange("crossfadeDuration", parseFloat(e.target.value))}
                                className="w-full"
                            />
                        </div>

                        {/* Audio Quality */}
                        <div>
                            <label className="block text-sm font-medium mb-2">Audio Quality</label>
                            <div className="grid grid-cols-3 gap-2">
                                {["low", "medium", "high"].map((quality) => (
                                    <button
                                        key={quality}
                                        onClick={() => handleSettingChange("audioQuality", quality)}
                                        className={cn(
                                            "px-4 py-2 rounded-md border transition-colors",
                                            audioSettings.audioQuality === quality
                                                ? "bg-blue-500 text-white border-blue-500"
                                                : "border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                                        )}
                                    >
                                        {quality.charAt(0).toUpperCase() + quality.slice(1)}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Toggle Settings */}
                        <div className="space-y-4">
                            {[
                                { key: "gaplessPlayback", label: "Gapless Playback", description: "Seamless transitions between tracks" },
                                { key: "normalizeVolume", label: "Normalize Volume", description: "Automatically adjust volume levels" },
                                { key: "replayGain", label: "ReplayGain", description: "Apply ReplayGain metadata for consistent volume" },
                                { key: "preloadNext", label: "Preload Next Track", description: "Load next track in advance for faster playback" },
                                { key: "fadeInOut", label: "Fade In/Out", description: "Smooth fade effects when starting/stopping" },
                            ].map((setting) => (
                                <div key={setting.key} className="flex items-center justify-between">
                                    <div>
                                        <p className="font-medium">{setting.label}</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">{setting.description}</p>
                                    </div>
                                    <button
                                        onClick={() => handleSettingChange(setting.key as any, !audioSettings[setting.key as keyof typeof audioSettings])}
                                        className={cn(
                                            "w-12 h-6 rounded-full transition-colors relative",
                                            audioSettings[setting.key as keyof typeof audioSettings]
                                                ? "bg-blue-500"
                                                : "bg-gray-300 dark:bg-gray-600"
                                        )}
                                    >
                                        <div className={cn(
                                            "w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform",
                                            audioSettings[setting.key as keyof typeof audioSettings]
                                                ? "translate-x-6"
                                                : "translate-x-0.5"
                                        )} />
                                    </button>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Equalizer Settings */}
                {activeTab === "equalizer" && (
                    <div className="space-y-6">
                        {/* Enable Equalizer */}
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="font-medium">Enable Equalizer</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400">Adjust frequency bands for custom sound</p>
                            </div>
                            <button
                                onClick={() => handleEqChange("enabled", !eqSettings.enabled)}
                                className={cn(
                                    "w-12 h-6 rounded-full transition-colors relative",
                                    eqSettings.enabled ? "bg-blue-500" : "bg-gray-300 dark:bg-gray-600"
                                )}
                            >
                                <div className={cn(
                                    "w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform",
                                    eqSettings.enabled ? "translate-x-6" : "translate-x-0.5"
                                )} />
                            </button>
                        </div>

                        {eqSettings.enabled && (
                            <>
                                {/* Presets */}
                                <div>
                                    <label className="block text-sm font-medium mb-2">Presets</label>
                                    <div className="grid grid-cols-3 gap-2">
                                        {Object.keys(EQ_PRESETS).map((preset) => (
                                            <button
                                                key={preset}
                                                onClick={() => applyEqPreset(preset as keyof typeof EQ_PRESETS)}
                                                className={cn(
                                                    "px-3 py-2 rounded-md border transition-colors text-sm",
                                                    eqSettings.preset === preset
                                                        ? "bg-blue-500 text-white border-blue-500"
                                                        : "border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                                                )}
                                            >
                                                {preset.charAt(0).toUpperCase() + preset.slice(1)}
                                            </button>
                                        ))}
                                    </div>
                                </div>

                                {/* EQ Bands */}
                                <div>
                                    <label className="block text-sm font-medium mb-4">Frequency Bands</label>
                                    <div className="grid grid-cols-5 gap-4">
                                        {Object.entries(eqSettings.bands).map(([freq, value]) => (
                                            <div key={freq} className="text-center">
                                                <div className="mb-2">
                                                    <input
                                                        type="range"
                                                        min="-12"
                                                        max="12"
                                                        step="0.5"
                                                        value={value}
                                                        onChange={(e) => handleEqBandChange(freq, parseFloat(e.target.value))}
                                                        className="w-full h-20 slider-vertical"
                                                        style={{ writingMode: "bt-lr", WebkitAppearance: "slider-vertical" }}
                                                    />
                                                </div>
                                                <div className="text-xs">
                                                    <div className="font-medium">{value > 0 ? '+' : ''}{value}dB</div>
                                                    <div className="text-gray-500">
                                                        {parseInt(freq) >= 1000 ? `${parseInt(freq) / 1000}kHz` : `${freq}Hz`}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                )}

                {/* Footer */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <Button variant="outline" onClick={onClose}>
                        Close
                    </Button>
                </div>
            </ModalBody>
        </Modal>
    );
}
