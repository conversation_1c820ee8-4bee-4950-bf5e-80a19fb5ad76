import { Fragment, useState, useEffect, useRef, useMemo } from "react";
import { Dialog, Transition } from "@headlessui/react";
import {
    Avatar,
    Button,
    InputField,
    LineProgressInputRange,
    SpinnerLoader,
} from "@components/lib/ui";
import {
    IoPause,
    IoPlaySkipForwardSharp,
    IoPlaySkipBackSharp,
    IoHeartOutline,
    IoPlay,
    IoHeart,
    IoChevronDown,
    IoShareSocial,
    SiDjango,
} from "@/components/lib/icons";
import useEmblaCarousel from "embla-carousel-react";
import { LuRepeat, LuRepeat1, LuShuffle } from "@/components/lib/icons";
import { asset, cn } from "@/utils";
import { useItemLike, useModal, useZiggy } from "@/hooks";
import { usePlayer, formatTime, PlayerHook, getTrackImage } from "./hook";
import { useDjMode } from "./dj-mode";
import { AudioSettingsButton } from "./audio-settings";
import { LyricsButton, CompactLyricsDisplay } from "./lyrics-display";
import { Link } from "@inertiajs/react";
import { Article } from "@/types/models";
import { CommentText, useComments } from "../lib";
import throttle from "lodash/throttle";
import { useSwipeToClose } from "./use-swipe-to-close";

function MobilePlayerModal({
    isOpen,
    onClose,
}: {
    isOpen: boolean;
    onClose: () => void;
}) {
    const player = usePlayer();

    if (!player?.currentTrack) {
        return null;
    }

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={onClose}>
                {/* Backdrop */}
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" />
                </Transition.Child>

                {/* Modal */}
                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-0">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-[90%]"
                            enterTo="opacity-100 translate-y-0"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0"
                            leaveTo="opacity-0 translate-y-[90%]"
                        >
                            <Dialog.Panel className="w-full h-screen bg-light-primary dark:bg-dark-primary overflow-hidden transition-all">
                                <ModalContent
                                    isOpen={isOpen}
                                    onClose={onClose}
                                />
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}

function ModalContent({
    onClose,
    isOpen,
}: {
    isOpen: boolean;
    onClose: () => void;
}) {
    const route = useZiggy();
    const { likeCall } = useItemLike();

    const player = usePlayer({ listenToSeek: true });
    const { djMode, hasNext: iHasNext, toggleDjMode } = useDjMode();

    const currentTrack = player?.currentTrack;
    const trackUser = currentTrack?.user;

    const [activeTab, setActiveTab] = useState<"player" | "queue" | "comments">(
        "player"
    );

    // --- Ref for the scrollable content area ---
    const scrollableContentRef = useRef<HTMLDivElement>(null);

    const swipeableElementRef = useSwipeToClose<HTMLDivElement>({
        onClose,
        isOpen,
        scrollableRef: scrollableContentRef, // Pass the ref of the scrollable div
        // threshold: 100, // Optional: customize threshold
        // disabled: activeTab !== 'player', // Optional: disable if not on player tab, for example
    });

    let channelLink = null;
    if (currentTrack) {
        channelLink = route("app.channels.show", {
            channel: currentTrack.channel.id,
            article: currentTrack.id,
        });
    }

    const trackImage = getTrackImage(currentTrack);

    useEffect(() => {
        if (isOpen) {
            setActiveTab("player");
        }
    }, [isOpen]);

    if (!currentTrack) {
        return null;
    }

    const hasNext = djMode ? iHasNext : player?.actions.hasNext;
    const hasPrev = player?.actions.hasPrev;
    const repeatState = player?.repeat;
    const isShuffle = player?.shuffle;
    const isPlaying = player?.isPlaying;
    const featurings = currentTrack.featurings || [];

    // share button
    const shareButton = () => {
        if (currentTrack && "share" in navigator) {
            navigator.share({
                title: currentTrack.name,
                text: `Check out this track: ${currentTrack.name}`,
                url: channelLink || "",
            });
        }
    };

    return (
        <div
            ref={swipeableElementRef}
            className="w-full h-screen transform text-left align-middle flex flex-col"
        >
            {/* Header */}
            <div className="sticky top-0 z-10 bg-light-primary/90 dark:bg-dark-primary/90 backdrop-blur-md shadow-sm">
                <div className="flex justify-between items-center px-4 pt-4 pb-2">
                    <button
                        onClick={onClose}
                        className="rounded-full outline-none p-2 hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors"
                        aria-label="Close player"
                    >
                        <IoChevronDown className="h-6 w-6 text-gray-600 dark:text-gray-300" />
                    </button>

                    <div className="text-center">
                        <h3 className="text-sm font-medium text-gray-600 dark:text-gray-300 tracking-wider">
                            NOW PLAYING
                        </h3>
                    </div>

                    <button
                        className="rounded-full outline-none p-2 hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors"
                        aria-label="Share track"
                        onClick={shareButton}
                    >
                        <IoShareSocial className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                    </button>
                </div>

                {/* Tabs */}
                <div className="flex border-b border-gray-200 dark:border-gray-800 px-4">
                    {(["player", "queue", "comments"] as const).map((tab) => (
                        <button
                            key={tab}
                            className={cn(
                                "flex-1 py-3 outline-none text-sm font-medium capitalize transition-colors",
                                activeTab === tab
                                    ? "text-primary-500 dark:text-primary border-b-2 border-primary-500 dark:border-primary"
                                    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            )}
                            onClick={() => {
                                setActiveTab(tab);
                            }}
                        >
                            {tab}
                        </button>
                    ))}
                </div>
            </div>

            {/* Main content */}
            <div
                className="flex-1 overflow-y-auto px-4 py-6" // Crucial: overflow-y-auto must be here
                ref={scrollableContentRef}
            >
                <div
                    className={cn(
                        "flex flex-col items-center space-y-4 h-full",
                        activeTab !== "player" && "hidden"
                    )}
                >
                    {/* Track Artwork */}
                    <CoverImage
                        hasNext={hasNext}
                        hidden={activeTab !== "player" || !isOpen}
                    />

                    {/* Track Info */}
                    <div className="w-full space-y-1 flex justify-between max-w-full overflow-hidden">
                        <div className="flex flex-col space-y-2 max-w-[80%]">
                            <h2 className="text-2xl font-bold text-dark dark:text-light line-clamp-2 break-words">
                                <Link
                                    href={channelLink || "#"}
                                    onClick={onClose}
                                >
                                    {currentTrack.name}
                                </Link>
                            </h2>

                            {/* Artist details */}
                            <p className="text-base text-gray-600 dark:text-gray-400 line-clamp-1 break-words">
                                {trackUser && (
                                    <Link
                                        onClick={onClose}
                                        href={route("app.artists.show", {
                                            user: trackUser?.id,
                                        })}
                                    >
                                        {trackUser?.name}
                                    </Link>
                                )}

                                {!trackUser && (
                                    <span className="text-gray-400">
                                        Unknown Artist
                                    </span>
                                )}

                                {featurings.map((artist) => {
                                    return (
                                        <Fragment key={artist.id}>
                                            <span>{", "}</span>
                                            <Link
                                                title={artist.name}
                                                onClick={onClose}
                                                href={route(
                                                    "app.artists.show",
                                                    {
                                                        user: artist.id,
                                                    }
                                                )}
                                            >
                                                {artist.name}
                                            </Link>
                                        </Fragment>
                                    );
                                })}
                            </p>
                        </div>

                        <div className="flex">
                            <LikeButton
                                liked={currentTrack.liked}
                                onClick={() => {
                                    if (currentTrack) {
                                        likeCall(
                                            {
                                                type: "article",
                                                data: currentTrack,
                                            },
                                            true
                                        );
                                    }
                                }}
                            />

                            <LyricsButton />

                            <AudioSettingsButton />

                            <ActionButton
                                aria-label="DJ Mode"
                                onClick={toggleDjMode}
                                className={cn(
                                    "outline-none",
                                    djMode && [
                                        "text-primary-600 dark:text-primary",
                                        "hover:text-primary-600 dark:hover:text-primary",
                                    ]
                                )}
                            >
                                <SiDjango
                                    title={
                                        djMode
                                            ? "Disable DJ Mode"
                                            : "Enable DJ Mode"
                                    }
                                    className="size-6"
                                />
                            </ActionButton>
                        </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full space-y-2">
                        <LineProgressInputRange
                            min={0}
                            value={player?.currentTime || 0}
                            max={player?.duration || 0}
                            disabled={!player}
                            onReleased={(value) => {
                                player?.actions.seekTo(value);
                            }}
                            className="h-2 rounded-full"
                        />
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                            <span>
                                {player?.formattedCurrentTime || "0:00"}
                            </span>
                            <span>{formatTime(player?.duration)}</span>
                        </div>
                    </div>

                    {/* Compact Lyrics Display */}
                    <CompactLyricsDisplay />

                    {/* Player Controls */}
                    <div className="w-full flex justify-between items-center">
                        {/* Shuffle and Repeat */}
                        <ControlButton
                            active={isShuffle}
                            onClick={() => {
                                player?.actions.toggleShuffle();
                            }}
                            aria-label={
                                isShuffle ? "Disable shuffle" : "Enable shuffle"
                            }
                        >
                            <LuShuffle className="h-5 w-5" />
                        </ControlButton>

                        {/* Main Controls */}
                        <div className="flex justify-center items-center space-x-6">
                            <SkipButton
                                direction="prev"
                                disabled={!hasPrev}
                                onClick={() => {
                                    player?.actions.playPrevTrack();
                                }}
                            />

                            <PlayPauseButton
                                isPlaying={isPlaying}
                                loading={player?.loading}
                                onClick={() => {
                                    player?.actions.togglePlay();
                                }}
                            />

                            <SkipButton
                                direction="next"
                                disabled={!hasNext}
                                onClick={() => {
                                    player?.actions.playNextTrack();
                                }}
                            />
                        </div>

                        <ControlButton
                            active={repeatState != "none"}
                            onClick={() => {
                                player?.actions.toggleRepeat();
                            }}
                            aria-label={getRepeatTitle(repeatState)}
                        >
                            {repeatState == "one" ? (
                                <LuRepeat1 className="h-5 w-5" />
                            ) : (
                                <LuRepeat className="h-5 w-5" />
                            )}
                        </ControlButton>
                    </div>
                </div>

                {activeTab === "queue" && <QueueContent player={player} />}

                {activeTab === "comments" && (
                    <CommentButton
                        key={currentTrack.id}
                        currentTrack={currentTrack}
                    />
                )}
            </div>

            {/* Mini Player */}
            {!["player", "comments"].includes(activeTab) && (
                <div
                    className="sticky bottom-0 bg-light-primary/95 dark:bg-dark-primary/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-800 p-4"
                    onClick={() => setActiveTab("player")}
                    role="button"
                >
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                            <img
                                src={asset(trackImage)}
                                alt={currentTrack.name}
                                className="w-12 h-12 rounded-lg shadow-sm"
                            />

                            <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-dark dark:text-light truncate">
                                    {currentTrack.name}
                                </h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                    {trackUser?.name || "Unknown Artist"}
                                </p>
                            </div>
                        </div>

                        <PlayPauseButton
                            isPlaying={isPlaying}
                            loading={player?.loading}
                            onClick={() => {
                                player?.actions.togglePlay();
                            }}
                            size="sm"
                        />
                    </div>
                </div>
            )}
        </div>
    );
}

function CoverImage({
    hasNext,
    hidden,
}: {
    hasNext: boolean;
    hidden: boolean;
}) {
    const player = usePlayer();

    const playlist = useMemo(() => player?.playlist || [], [player?.playlist]);
    const currentTrackIndex = player?.currentTrackIndex ?? -1;

    const [isHidden, setIsHidden] = useState(hidden);

    const [emblaRef, emblaApi] = useEmblaCarousel({
        loop: hasNext,
        align: "center",
        watchSlides: true,
        skipSnaps: false,
    });

    // Flag to prevent initial scroll jump if index is already 0
    const hasScrolledInitially = useRef(false);
    // Ref to prevent loop between player update and embla select event
    const pointerDownTrigged = useRef(false);
    const isPlayerSelectTriggered = useRef(false);

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsHidden(hidden);
        }, 100);

        return () => {
            clearTimeout(timer);
        };
    }, [hidden]);

    useEffect(() => {
        if (emblaApi && currentTrackIndex !== -1 && !isHidden) {
            // If the change was triggered by the carrousel update itself
            if (isPlayerSelectTriggered.current) {
                isPlayerSelectTriggered.current = false;
                return;
            }

            const selectedSnap = emblaApi.selectedScrollSnap();

            if (
                selectedSnap !== currentTrackIndex ||
                !hasScrolledInitially.current
            ) {
                emblaApi.scrollTo(
                    currentTrackIndex,
                    !hasScrolledInitially.current
                );
                hasScrolledInitially.current = true;
            }
        }
    }, [emblaApi, currentTrackIndex, isHidden]);

    useEffect(() => {
        return () => {
            if (emblaApi) {
                emblaApi.destroy();
            }
        };
    }, [emblaApi]);

    useEffect(() => {
        if (!emblaApi) return;

        const onSelect = () => {
            if (!player || !pointerDownTrigged.current) {
                return;
            }
            pointerDownTrigged.current = false;

            const selectedIndex = emblaApi.selectedScrollSnap();

            if (selectedIndex !== currentTrackIndex) {
                isPlayerSelectTriggered.current = true;
                player.actions.playTrack(selectedIndex);
            }
        };

        const onPointerDown = () => {
            pointerDownTrigged.current = true;
        };

        const onPointerUp = throttle(() => {
            pointerDownTrigged.current = false;
        }, 100);

        emblaApi.on("select", onSelect);
        emblaApi.on("pointerDown", onPointerDown);
        emblaApi.on("pointerUp", onPointerUp);
        return () => {
            emblaApi.off("select", onSelect);
            emblaApi.off("pointerDown", onPointerDown);
            emblaApi.off("pointerUp", onPointerUp);
        };
    }, [emblaApi, currentTrackIndex]);

    if (!player || playlist.length === 0) {
        return (
            <div className="relative aspect-square rounded-2xl overflow-hidden shadow-xl h-[45vh] bg-neutral-700 flex items-center justify-center mx-auto">
                <span className="text-neutral-400">No tracks</span>
            </div>
        );
    }

    return (
        <div className="overflow-hidden max-w-full" ref={emblaRef}>
            <div className="flex items-center w-full gap-4 h-[45vh]">
                {playlist.map((track) => {
                    const trackImage = getTrackImage(track);

                    return (
                        <div
                            key={track.id}
                            className={cn(
                                "relative aspect-square rounded-2xl overflow-hidden shadow-xl",
                                "w-full h-full flex-none"
                            )}
                        >
                            {trackImage && (
                                <img
                                    src={asset(trackImage)}
                                    alt={track.name}
                                    className="w-full h-full object-cover"
                                    draggable="false"
                                />
                            )}

                            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
                        </div>
                    );
                })}
            </div>
        </div>
    );
}

// Sub-components
function ControlButton({
    children,
    active,
    ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & { active?: boolean }) {
    return (
        <button
            {...props}
            className={cn(
                "p-2 rounded-full text-gray-600 dark:text-gray-400 outline-none transition-colors",
                {
                    "text-primary-500 dark:text-primary hover:text-primary-500 dark:text-primary/80":
                        active,
                },
                props.className
            )}
        >
            {children}
        </button>
    );
}

function PlayPauseButton({
    isPlaying,
    loading,
    onClick,
    size = "md",
}: {
    isPlaying?: boolean;
    loading?: boolean;
    onClick?: () => void;
    size?: "md" | "sm";
}) {
    const sizes = {
        md: "h-14 w-14",
        sm: "h-10 w-10",
    };

    return (
        <button
            className={cn(
                "flex items-center justify-center bg-primary rounded-full shadow-lg hover:bg-primary/90 transition-colors",
                "disabled:opacity-70 disabled:cursor-not-allowed outline-none",
                sizes[size]
            )}
            onClick={onClick}
            disabled={loading}
            aria-label={isPlaying ? "Pause" : "Play"}
        >
            {loading ? (
                <SpinnerLoader size={size === "md" ? 24 : 20} variant="light" />
            ) : isPlaying ? (
                <IoPause
                    className={`${
                        size === "md" ? "h-7 w-7" : "h-5 w-5"
                    } text-white`}
                />
            ) : (
                <IoPlay
                    className={cn(
                        size === "md" ? "h-7 w-7 ml-0.5" : "h-5 w-5 ml-0.5",
                        "text-white"
                    )}
                />
            )}
        </button>
    );
}

function SkipButton({
    direction,
    ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & {
    direction: "prev" | "next";
}) {
    const Icon =
        direction === "prev" ? IoPlaySkipBackSharp : IoPlaySkipForwardSharp;
    return (
        <ControlButton
            className={cn(
                "p-2",
                props.disabled && "opacity-40 cursor-not-allowed"
            )}
            {...props}
        >
            <Icon className="h-6 w-6" />
        </ControlButton>
    );
}

function LikeButton({
    liked,
    onClick,
}: {
    liked?: boolean;
    onClick?: () => void;
}) {
    return (
        <ActionButton
            onClick={onClick}
            aria-label={liked ? "Unlike track" : "Like track"}
        >
            {liked ? (
                <IoHeart className="h-6 w-6 text-primary-500 dark:text-primary" />
            ) : (
                <IoHeartOutline className="h-6 w-6" />
            )}
        </ActionButton>
    );
}

function ActionButton(props: React.ButtonHTMLAttributes<HTMLButtonElement>) {
    return (
        <button
            {...props}
            className={cn(
                "p-2 rounded-full outline-none text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",
                props.className
            )}
        />
    );
}

// comment button
function CommentButton({ currentTrack }: { currentTrack: Article }) {
    const {
        comments,
        loading,
        text,
        setText,
        handleCreate,
        fetchComment,
        setComments,
        setCount,
        nextUrl,
    } = useComments({ type: "channel", data: currentTrack.channel });

    return (
        <div className="h-full flex flex-col space-y-4 text-dark dark:text-light relative">
            {comments.map((data) => {
                return (
                    <CommentText
                        onDelete={(id) => {
                            setComments((ps) => ps.filter((c) => c.id !== id));
                            setCount((c) => c - 1);
                        }}
                        key={data.id}
                        comment={data}
                    />
                );
            })}

            {nextUrl && (
                <div className="w-full flex">
                    <Button
                        variant="outline"
                        loading={loading}
                        disabled={loading}
                        className="border border-primary text-sm"
                        onClick={() => fetchComment(nextUrl)}
                    >
                        Load more
                    </Button>
                </div>
            )}

            {!nextUrl && loading && (
                <div className="w-full flex justify-center">
                    <SpinnerLoader />
                </div>
            )}

            <form
                action="post"
                className="w-full sticky bottom-0 backdrop-blur-md mt-3 top-[92%]"
                onSubmit={handleCreate}
            >
                <InputField
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    placeholder="Add comment"
                />
            </form>
        </div>
    );
}

// Queue content component
function QueueContent({ player }: { player?: PlayerHook }) {
    const queue = player?.playlist || [];
    const currentIndex = player?.currentTrackIndex ?? -1;

    useEffect(() => {
        const activeQueueITem = document.querySelector(
            "#mobile-queue-content .active-item-queue"
        );
        activeQueueITem?.scrollIntoView({
            behavior: "instant",
            block: "center",
        });
    }, []);

    return (
        <div className="space-y-4" id="mobile-queue-content">
            <h3 className="text-lg font-semibold text-dark dark:text-light">
                Up Next
            </h3>

            {queue.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 text-gray-500 dark:text-gray-400">
                    <p>Your queue is empty</p>
                </div>
            ) : (
                <div className="space-y-2">
                    {queue.map((track, index) => {
                        const image = getTrackImage(track);
                        const featurings = track.featurings || [];

                        return (
                            <div
                                key={`${track.id}-${index}`}
                                className={cn(
                                    "flex items-center p-3 rounded-lg transition-colors cursor-pointer",
                                    "hover:bg-gray-100 dark:hover:bg-gray-800/50",
                                    index === currentIndex && [
                                        "bg-primary/10 border-l-4 border-primary-500 dark:border-primary",
                                        "active-item-queue",
                                    ]
                                )}
                                onClick={() => player?.actions.playTrack(index)}
                            >
                                <img
                                    src={asset(image)}
                                    alt={track.name}
                                    className="w-12 h-12 rounded-lg mr-3"
                                />

                                <div className="flex-1 min-w-0">
                                    <h4
                                        className={cn(
                                            "font-medium truncate",
                                            index === currentIndex
                                                ? "text-primary-500 dark:text-primary"
                                                : "text-dark dark:text-light"
                                        )}
                                    >
                                        {track.name}
                                    </h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                        <span>
                                            {track.user?.name ||
                                                "Unknown Artist"}
                                        </span>
                                        {featurings.map((artist) => {
                                            return (
                                                <Fragment key={artist.id}>
                                                    <span>{", "}</span>
                                                    <span>{artist.name}</span>
                                                </Fragment>
                                            );
                                        })}
                                    </p>
                                </div>

                                {index === currentIndex && (
                                    <div className="ml-2 text-primary-500 dark:text-primary">
                                        <IoPlay className="h-4 w-4" />
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            )}
        </div>
    );
}

// Helper function remains the same
function getRepeatTitle(repeatState: string) {
    switch (repeatState) {
        case "none":
            return "Repeat";
        case "one":
            return "Repeat One";
        case "all":
            return "Repeat All";
        default:
            return "Repeat";
    }
}

export function MusicPlayerModalButton({ className }: { className?: string }) {
    const modal = useModal();
    const player = usePlayer();

    if (!player?.currentTrack) {
        return null;
    }

    const currentTrack = player.currentTrack;
    const featurings = currentTrack.featurings || [];
    const trackImage = getTrackImage(currentTrack);

    return (
        <>
            <button
                onClick={() => modal.openModal()}
                className={cn(
                    "flex items-center justify-between gap-2 py-3 rounded-lg",
                    className
                )}
            >
                <div className="flex flex-1 space-x-2 min-w-0">
                    {trackImage && (
                        <img
                            src={asset(trackImage)}
                            alt={player.currentTrack.name}
                            className="size-10 rounded object-cover"
                        />
                    )}

                    {!trackImage && (
                        <Avatar
                            imageTitle={player.currentTrack.name}
                            className={cn(
                                "bg-light-primary dark:bg-dark-primary cursor-pointer",
                                "size-10"
                            )}
                        />
                    )}

                    <div className="text-left flex space-y-1 flex-col min-w-0">
                        <p className="font-medium truncate max-w-32 text-gray-600 dark:text-gray-400">
                            {player.currentTrack.name}
                        </p>

                        <p className="text-xs truncate max-w-32">
                            {player.currentTrack.user?.name || "Unknown Artist"}
                            {featurings.map((artist) => {
                                return (
                                    <Fragment key={artist.id}>
                                        <span>{", "}</span>
                                        <span>{artist.name}</span>
                                    </Fragment>
                                );
                            })}
                        </p>
                    </div>
                </div>

                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        player?.actions.togglePlay();
                    }}
                >
                    {player.isPlaying ? (
                        <IoPause className="h-6 w-6" />
                    ) : (
                        <IoPlay className="h-6 w-6" />
                    )}
                </button>
            </button>

            <MobilePlayerModal
                isOpen={modal.isOpen}
                onClose={modal.closeModal}
            />
        </>
    );
}
