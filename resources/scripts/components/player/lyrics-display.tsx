import { useState, useEffect } from "react";
import { usePlayer } from "./hook";
import { <PERSON><PERSON>, Modal, ModalBody } from "@/components/lib/ui";
import { IoMusicalNotes, IoClose } from "@/components/lib/icons";
import { cn } from "@/utils";
import { useModal } from "@/hooks";

interface LyricsLine {
    time: number;
    text: string;
}

interface LyricsData {
    title: string;
    artist: string;
    lines: LyricsLine[];
}

// Mock lyrics data - in a real app, this would come from an API
const MOCK_LYRICS: Record<string, LyricsData> = {
    "default": {
        title: "Sample Song",
        artist: "Sample Artist",
        lines: [
            { time: 0, text: "♪ Instrumental ♪" },
            { time: 10, text: "This is a sample lyric line" },
            { time: 15, text: "Another line of lyrics here" },
            { time: 20, text: "Music brings us together" },
            { time: 25, text: "In harmony and rhythm" },
            { time: 30, text: "♪ Instrumental break ♪" },
            { time: 40, text: "Final verse begins now" },
            { time: 45, text: "Thank you for listening" },
        ]
    }
};

export function LyricsButton() {
    const modal = useModal();
    const player = usePlayer();
    const currentTrack = player?.currentTrack;

    if (!currentTrack) {
        return null;
    }

    return (
        <>
            <button
                onClick={modal.openModal}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                title="Show Lyrics"
            >
                <IoMusicalNotes className="h-5 w-5" />
            </button>

            <LyricsModal
                isOpen={modal.isOpen}
                onClose={modal.closeModal}
                track={currentTrack}
            />
        </>
    );
}

interface LyricsModalProps {
    isOpen: boolean;
    onClose: () => void;
    track: any;
}

function LyricsModal({ isOpen, onClose, track }: LyricsModalProps) {
    const player = usePlayer({ listenToSeek: true });
    const [lyrics, setLyrics] = useState<LyricsData | null>(null);
    const [currentLineIndex, setCurrentLineIndex] = useState(-1);
    const [loading, setLoading] = useState(false);

    // Load lyrics when track changes
    useEffect(() => {
        if (!track || !isOpen) return;

        setLoading(true);
        // Simulate API call
        setTimeout(() => {
            // In a real app, you'd fetch lyrics from an API
            const trackLyrics = MOCK_LYRICS[track.id] || MOCK_LYRICS["default"];
            setLyrics({
                ...trackLyrics,
                title: track.name,
                artist: track.user?.name || "Unknown Artist"
            });
            setLoading(false);
        }, 500);
    }, [track?.id, isOpen]);

    // Update current line based on playback time
    useEffect(() => {
        if (!lyrics || !player?.currentTime) return;

        const currentTime = player.currentTime;
        let activeIndex = -1;

        for (let i = 0; i < lyrics.lines.length; i++) {
            if (currentTime >= lyrics.lines[i].time) {
                activeIndex = i;
            } else {
                break;
            }
        }

        setCurrentLineIndex(activeIndex);
    }, [player?.currentTime, lyrics]);

    const handleLineClick = (time: number) => {
        if (player?.actions.seekTo) {
            player.actions.seekTo(time);
        }
    };

    return (
        <Modal isOpen={isOpen} closeModal={onClose}>
            <ModalBody onClose={onClose} className="max-w-2xl max-h-[80vh]">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h2 className="text-xl font-semibold">Lyrics</h2>
                        {lyrics && (
                            <p className="text-gray-600 dark:text-gray-400">
                                {lyrics.title} - {lyrics.artist}
                            </p>
                        )}
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                    >
                        <IoClose className="h-5 w-5" />
                    </button>
                </div>

                <div className="overflow-y-auto max-h-96">
                    {loading ? (
                        <div className="text-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                            <p className="text-gray-600 dark:text-gray-400">Loading lyrics...</p>
                        </div>
                    ) : lyrics ? (
                        <div className="space-y-3">
                            {lyrics.lines.map((line, index) => (
                                <div
                                    key={index}
                                    onClick={() => handleLineClick(line.time)}
                                    className={cn(
                                        "p-3 rounded-lg cursor-pointer transition-all duration-300",
                                        "hover:bg-gray-50 dark:hover:bg-gray-700",
                                        index === currentLineIndex
                                            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-100 font-medium scale-105"
                                            : index < currentLineIndex
                                            ? "text-gray-500 dark:text-gray-500"
                                            : "text-gray-900 dark:text-gray-100"
                                    )}
                                >
                                    <div className="flex items-center space-x-3">
                                        <span className="text-xs text-gray-400 min-w-[40px]">
                                            {Math.floor(line.time / 60)}:{(line.time % 60).toString().padStart(2, '0')}
                                        </span>
                                        <span className="flex-1">{line.text}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <IoMusicalNotes className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                No lyrics available
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400">
                                Lyrics for this track are not available at the moment.
                            </p>
                        </div>
                    )}
                </div>

                {lyrics && (
                    <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                            <span>Click on any line to jump to that time</span>
                            <span>
                                {currentLineIndex + 1} / {lyrics.lines.length}
                            </span>
                        </div>
                    </div>
                )}
            </ModalBody>
        </Modal>
    );
}

// Compact lyrics display for the player
export function CompactLyricsDisplay() {
    const player = usePlayer({ listenToSeek: true });
    const [lyrics, setLyrics] = useState<LyricsData | null>(null);
    const [currentLineIndex, setCurrentLineIndex] = useState(-1);

    const currentTrack = player?.currentTrack;

    // Load lyrics when track changes
    useEffect(() => {
        if (!currentTrack) return;

        // In a real app, you'd fetch lyrics from an API
        const trackLyrics = MOCK_LYRICS[currentTrack.id] || MOCK_LYRICS["default"];
        setLyrics({
            ...trackLyrics,
            title: currentTrack.name,
            artist: currentTrack.user?.name || "Unknown Artist"
        });
    }, [currentTrack?.id]);

    // Update current line based on playback time
    useEffect(() => {
        if (!lyrics || !player?.currentTime) return;

        const currentTime = player.currentTime;
        let activeIndex = -1;

        for (let i = 0; i < lyrics.lines.length; i++) {
            if (currentTime >= lyrics.lines[i].time) {
                activeIndex = i;
            } else {
                break;
            }
        }

        setCurrentLineIndex(activeIndex);
    }, [player?.currentTime, lyrics]);

    if (!lyrics || currentLineIndex === -1) {
        return null;
    }

    const currentLine = lyrics.lines[currentLineIndex];
    const nextLine = lyrics.lines[currentLineIndex + 1];

    return (
        <div className="text-center py-2 px-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {currentLine.text}
            </div>
            {nextLine && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {nextLine.text}
                </div>
            )}
        </div>
    );
}
