import { ArticleLook, ITab, Tabs } from "@/components/lib/ui";
import { SearchPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";
import { DiscoverChannelItems, TopChartArticles } from "../discover";
import { CategoryItem, UserCard, PlaylistCard } from "@/components/lib";

export function SearchResult({ query = "" }) {
    const { props } = usePage<SearchPageProps>();
    const search = props.search!;

    const tabs: ITab[] = [
        { title: `Songs (${search?.articles.length || 0})` },
        { title: `Albums|Channels (${search?.channels.length || 0})` },
        { title: `Playlists (${search?.playlists?.length || 0})` },
        { title: `Artists (${search?.artists.length || 0})` },
        { title: `Genres (${search?.genres.length || 0})` },
    ];

    let defaultIndex: null | number = null;

    // Order of keys is important
    const seackKeys = ["articles", "channels", "playlists", "artists", "genres"] as const;

    seackKeys.forEach((key, idx) => {
        const data = search[key] || [];
        if (data.length > 0) {
            defaultIndex === null && (defaultIndex = idx);
            tabs[idx].disabled = false;
        } else {
            tabs[idx].disabled = true;
        }
    });

    return (
        <>
            <h3 className="font-normal text-xl mb-4">Search result</h3>

            <div className="w-full lg:w-4/5">
                <Tabs tabs={tabs} defaultIndex={defaultIndex || 0} key={query}>
                    {(Panel) => {
                        return (
                            <div className="mt-4">
                                {/* Articles */}
                                <Panel>
                                    <TopChartArticles
                                        articles={search.articles || []}
                                    />
                                </Panel>
                                {/* Channels or Albums */}
                                <Panel>
                                    <DiscoverChannelItems
                                        cols={6}
                                        channels={search.channels || []}
                                    />
                                </Panel>
                                {/* Playlists */}
                                <Panel>
                                    <ArticleLook
                                        className="h-max"
                                        type="card"
                                        cols={6}
                                    >
                                        {(search.playlists || []).map((playlist) => {
                                            return (
                                                <PlaylistCard
                                                    key={playlist.id}
                                                    playlist={playlist}
                                                />
                                            );
                                        })}
                                    </ArticleLook>
                                </Panel>
                                {/* Artists */}
                                <Panel>
                                    <ArticleLook
                                        className="h-max"
                                        type="card"
                                        cols={6}
                                    >
                                        {search.artists.map((user) => {
                                            return (
                                                <UserCard
                                                    layout="fill"
                                                    user={user}
                                                    key={user.id}
                                                />
                                            );
                                        })}
                                    </ArticleLook>
                                </Panel>
                                {/* Genres */}
                                <Panel>
                                    <ArticleLook type="card" cols={6}>
                                        {search.genres.map((genre) => {
                                            return (
                                                <CategoryItem
                                                    key={genre.id}
                                                    genre={genre}
                                                />
                                            );
                                        })}
                                    </ArticleLook>
                                </Panel>
                            </div>
                        );
                    }}
                </Tabs>
            </div>
        </>
    );
}
