import { useIPage } from "@/hooks";
import { IoMusicalNotes, IoHeadset, IoRadio } from "react-icons/io5";

const features = [
    {
        icon: <IoMusicalNotes className="animate-pulse" />,
        title: "Global Music Library",
        description: "Access diverse songs from displaced artists worldwide.",
    },
    {
        icon: <IoHeadset />,
        title: "High-Quality Audio",
        description: "Experience premium sound with advanced technology.",
    },
    {
        icon: <IoRadio />,
        title: "Curated Playlists",
        description: "Discover music tailored to your taste and culture.",
    },
];

export function FeaturesSection() {
    const { props } = useIPage();
    // const route = useZiggy();

    return (
        <div className="w-full h-full flex items-center justify-center relative bg-gradient-to-b from-white to-primary-50 overflow-y-auto">
            <div className="w-full max-w-6xl mx-auto px-3 lg:px-4 py-4 lg:py-6">
                {/* Compact Header with Mission */}
                <div className="text-center mb-6 lg:mb-8">
                    <h2 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold mb-3">
                        Empowering Displaced Artists
                    </h2>
                    <p className="text-xs sm:text-sm lg:text-base text-gray-700 leading-tight max-w-3xl mx-auto">
                        A platform for refugee, displaced, and migrant artists
                        to showcase their stories, sounds, and music while
                        building community and gaining support.
                    </p>
                </div>

                {/* Two-Column Layout for Better Space Usage */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                    {/* Left Column: Features */}
                    <div className="space-y-4">
                        <h3 className="text-base sm:text-lg lg:text-xl font-bold text-center lg:text-left">
                            Why Choose {props.appName}?
                        </h3>

                        <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-1 gap-3">
                            {features.map((feature, index) => (
                                <div
                                    key={index}
                                    className="bg-white rounded-lg p-3 lg:p-4 shadow-sm border border-gray-100 flex lg:flex-row flex-col lg:items-center gap-3"
                                >
                                    <div className="text-primary text-xl lg:text-2xl flex-shrink-0 lg:self-start">
                                        {feature.icon}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="text-sm lg:text-base font-bold mb-1">
                                            {feature.title}
                                        </h4>
                                        <p className="text-xs lg:text-sm text-gray-600 leading-tight">
                                            {feature.description}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Right Column: About & Mission Details */}
                    <div className="space-y-4">
                        <h3 className="text-base sm:text-lg lg:text-xl font-bold text-center lg:text-left">
                            About {props.appName}
                        </h3>

                        <div className="bg-white rounded-lg p-3 lg:p-4 shadow-sm border border-gray-100 space-y-3">
                            <p className="text-xs lg:text-sm text-gray-700 leading-tight">
                                {props.appName} is a civic engagement project
                                providing a digital space for displaced artists
                                to showcase their talents and preserve their
                                creative heritage.
                            </p>

                            <div className="space-y-2">
                                <h4 className="text-sm font-semibold text-gray-900">
                                    Our Mission:
                                </h4>
                                <ul className="text-xs lg:text-sm text-gray-600 space-y-1">
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary mt-0.5">
                                            •
                                        </span>
                                        <span>
                                            Amplify voices of displaced artists
                                            worldwide
                                        </span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary mt-0.5">
                                            •
                                        </span>
                                        <span>
                                            Provide tools for storytelling and
                                            healing
                                        </span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary mt-0.5">
                                            •
                                        </span>
                                        <span>
                                            Build supportive creative
                                            communities
                                        </span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary mt-0.5">
                                            •
                                        </span>
                                        <span>
                                            Enable financial and social support
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
