import { PropsWithChildren } from "react";
import { cn } from "@/utils";
import { Sidebar, TopBar } from "./app-layout/index";
import { Authenticated, FlashMessages, UnAuthenticated } from "@components/lib";
import { AuthForm } from "@components/pages/auth";
import { useAuthUser, useOnboarding } from "@/hooks";
import { Head } from "@inertiajs/react";
import { AppHead } from "./app-head";
import { MusicPlayer, usePlayer } from "../player";
import ReloadPrompt from "../pwa/reload-prompt";
import InstallPwaModal from "../pwa/install-pwa-button";
import { WelcomeModal } from "../onboarding/welcome-modal";

export function AppLayout(
    props: PropsWithChildren<{
        minify?: boolean;
        forceRenderChildren?: boolean;
    }>
) {
    const auth = useAuthUser();
    const { showWelcomeModal, markWelcomeShown, completeOnboarding } =
        useOnboarding();

    return (
        <div
            className={cn(
                "text-dark dark:text-light",
                "bg-light-primary dark:bg-dark-primary",
                "min-h-screen min-w-full font-normal antialiased",
                "ease-out duration-300",
                "relative"
            )}
        >
            <MetaHeader />

            <div className="h-full pb-32">
                {auth && <Sidebar minify={props.minify} />}
                <div
                    className={cn(
                        "ml-14 md:ml-52 xl:ml-56",
                        !auth && ["ml-4 md:ml-12 xl:ml-12"],
                        props.minify && ["ml-4"]
                    )}
                >
                    <TopBar minify={props.minify} className="pr-4 md:pr-12" />

                    <main className="flex pt-5 mr-4 md:mr-12">
                        {props.forceRenderChildren ? (
                            props.children
                        ) : (
                            <>
                                <Authenticated>{props.children}</Authenticated>

                                <UnAuthenticated>
                                    <AppHead />
                                    <AuthForm />
                                </UnAuthenticated>
                            </>
                        )}
                    </main>
                </div>
            </div>

            <Player />
            <FlashMessages />

            {/* Onboarding */}
            {auth && (
                <WelcomeModal
                    isOpen={showWelcomeModal}
                    onClose={markWelcomeShown}
                    onComplete={completeOnboarding}
                    userName={auth.name || "there"}
                    isNewUser={auth.is_new_user || false}
                />
            )}

            {/* PWA */}
            <ReloadPrompt />
            <InstallPwaModal />
        </div>
    );
}

function Player() {
    const auth = useAuthUser();

    return (
        <div
            className={cn(
                "fixed px-5 sm:px-7 md:px-12 inset-x-0 bottom-0 z-50 md:text-sm text-xs ",
                "bg-light-primary-background dark:bg-dark-primary-background text-light-text-color dark:text-dark-text-color",
                "ease-out duration-300 touch-none",
                // "mb-10 md:mb-0",
                !auth && ["hidden md:block"]
            )}
        >
            <MusicPlayer />
        </div>
    );
}

function MetaHeader() {
    const props = usePlayer();
    const audio = props?.currentTrack?.audio;

    return (
        <>
            {audio && (
                <Head>
                    <meta property="og:audio" content={`/audio/${audio.id}`} />
                    <meta property="og:audio:type" content={audio.type} />
                </Head>
            )}
        </>
    );
}
