import { Link, usePage } from "@inertiajs/react";
import { BkeLogo } from "@components/lib/ui";
import { PropsWithChildren } from "react";
import { useAuthUser, useIPage, useZiggy } from "@/hooks";
import { cn } from "@/utils";

import {
    IoGridOutline,
    IoSearchOutline,
    IoHeartOutline,
    IoGrid,
    IoSearch,
    IoHeart,
    IoTrophyOutline,
    IoTrophy,
    IoLibraryOutline,
    IoLibrary,
    IoChatbubbleOutline,
    IoChatbubble,
    IoCloudDownload,
    IoCloudOffline,
    IoPeopleOutline,
    IoPeople,
} from "@/components/lib/icons";

const rmTrailingSlash = (str: string) => {
    const s = str[str.length - 1] === "/" ? str.slice(0, str.length - 1) : str;
    return s.substring(0, s.indexOf("?") === -1 ? s.length : s.indexOf("?"));
};

export function Sidebar({ minify }: { minify?: boolean }) {
    const { url } = useIPage();
    const route = useZiggy();
    const auth = useAuthUser();
    const { votingActive } = usePage<{ votingActive: boolean }>().props;

    const menus = [
        {
            text: "Discover",
            href: route("app.discover"),
            Icon: IoGridOutline,
            ActiveIcon: IoGrid,
        },
        {
            text: "Search",
            href: route("app.search"),
            Icon: IoSearchOutline,
            ActiveIcon: IoSearch,
        },
        // {
        //     text: "Artists",
        //     href: route("app.artists"),
        //     Icon: IoDiscOutline,
        //     ActiveIcon: IoDisc,
        // },
        // {
        //     text: "Albums",
        //     href: route("app.channels"),
        //     Icon: IoAlbumsOutline,
        //     ActiveIcon: IoAlbums,
        // },
        // {
        //     text: "Podcasts",
        //     href: route("app.podcasts"),
        //     Icon: IoMicCircleOutline,
        //     ActiveIcon: IoMicCircle,
        // },
        {
            text: "Favorites",
            href: route("app.favorites"),
            Icon: IoHeartOutline,
            ActiveIcon: IoHeart,
            active: !!auth,
        },
        {
            text: "Your Library",
            href: route("app.library"),
            Icon: IoLibraryOutline,
            ActiveIcon: IoLibrary,
            active: !!auth,
        },
        {
            text: "Messages",
            href: route("app.messages.index"),
            Icon: IoChatbubbleOutline,
            ActiveIcon: IoChatbubble,
            active: !!auth,
        },
        {
            text: "Offline Music",
            href: route("app.offline.index"),
            Icon: IoCloudDownload,
            ActiveIcon: IoCloudOffline,
            active: !!auth,
        },
        {
            text: "Social Feed",
            href: route("app.social.feed"),
            Icon: IoPeopleOutline,
            ActiveIcon: IoPeople,
            active: !!auth,
        },
        // Award menu item - only shown when there are active competitions
        {
            text: "Awards",
            href: route("app.voting.index"),
            Icon: IoTrophyOutline,
            ActiveIcon: IoTrophy,
            active: votingActive && !!auth,
        },
    ];

    return (
        <div
            className={cn(
                "fixed inset-y-0 left-0 app-sidebar z-40",
                "ease-out duration-300 touch-none",
                minify && ["flex flex-col items-center justify-center"],
                (!auth || minify) && ["hidden md:block"]
            )}
        >
            <div
                className={cn(
                    "py-5 w-14 md:w-48 flex items-center flex-col",
                    minify && ["justify-center h-full"]
                )}
            >
                {/* Logo */}
                <Link
                    href={route("app.discover")}
                    className={cn(
                        "md:w-20 md:scale-50 origin-top md:block -ml-2 md:-ml-8",
                        minify && ["hidden md:block"]
                    )}
                >
                    <BkeLogo className="fill-primary w-5 md:w-auto" />
                </Link>

                {/* Nav list */}
                <ul
                    className={cn(
                        "flex flex-col items-center md:items-start space-y-9 md:space-y-5 md:mt-3 font-normal -ml-2 md:-ml-8",
                        "mt-24 md:mt-15",
                        minify && ["hidden"]
                    )}
                >
                    {menus
                        .filter((m) => [undefined, true].includes(m.active))
                        .map((Menu, i) => {
                            const path = rmTrailingSlash(
                                new URL(Menu.href).pathname
                            );
                            const nUrl = rmTrailingSlash(url);
                            const active =
                                i === 0 ? path === nUrl : url.includes(path);
                            const IClassName = "h-5 w-auto md:h-5";

                            return (
                                <ItemLink
                                    key={i}
                                    href={Menu.href}
                                    active={active}
                                >
                                    {!active && (
                                        <Menu.Icon
                                            title={Menu.text}
                                            className={IClassName}
                                        />
                                    )}

                                    {active && (
                                        <Menu.ActiveIcon
                                            title={Menu.text}
                                            className={IClassName}
                                        />
                                    )}

                                    <span className="hidden md:inline-block">
                                        {Menu.text}
                                    </span>
                                </ItemLink>
                            );
                        })}
                </ul>
            </div>
        </div>
    );
}

type TItemLink = {
    active?: boolean;
    href: string;
};
function ItemLink({ children, href, active }: PropsWithChildren<TItemLink>) {
    return (
        <li>
            <Link
                href={href}
                className={cn(
                    "flex items-center space-x-2 cursor-pointer hover:text-primary-600/90 hover:dark:text-light/80 dark:text-light/50",
                    "overflow-hidden overflow-ellipsis transition-all",
                    active && ["dark:text-light/100 text-primary-600"]
                )}
            >
                {children}
            </Link>
        </li>
    );
}
