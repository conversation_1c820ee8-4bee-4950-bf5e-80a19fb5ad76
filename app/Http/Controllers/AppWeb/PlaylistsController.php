<?php

namespace App\Http\Controllers\AppWeb;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Article\ArticleResource;
use App\Http\Resources\Playlist\PlaylistResource;
use App\Models\Article;
use App\Models\File\Image;
use App\Models\Playlist\Playlist;
use App\Repositories\ArticleRepository;
use App\Repositories\PlaylistRepository;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\File;

class PlaylistsController extends Controller
{
    use HasFileRelation, HasRetriever;

    public function __construct(
        private PlaylistRepository $playlistRepository
    ) {}

    /**
     * Handle the incoming request.
     */
    public function show(string $playlist, Request $request)
    {
        $playlist = Playlist::find($playlist);
        if ($playlist === null) {
            return redirect()->route('app.discover');
        }

        $resource = $this->retrieve([$playlist, $request], [
            'playlist' => [$this->playlistRepository, 'getPlaylistDetails'],
            'articles' => [$this, 'articles'],
        ]);

        return inertia('app/playlists/show', $resource);
    }

    public function create(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
            'description' => ['nullable', 'string', 'max:1000'],
            'is_public' => ['boolean'],
        ]);

        $playlist = Playlist::create([
            ...$data,
            'user_id' => $request->user()->id,
            'is_system_generated' => false,
        ]);

        if ($request->hasFile('image')) {
            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $playlist->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $playlist->refresh();

        return new PlaylistResource($playlist);
    }

    /**
     * Handle the XHR incoming request.
     */
    public function view(Playlist $playlist)
    {
        return new PlaylistResource($playlist);
    }

    /**
     * Update playlist details
     */
    public function update(Playlist $playlist, Request $request)
    {
        $this->authorize('update', $playlist);

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'is_public' => ['boolean'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ]);

        $playlist->update($data);

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($playlist->image) {
                $playlist->image->delete();
            }

            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $playlist->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $playlist->refresh();

        return new PlaylistResource($playlist);
    }

    /**
     * Delete playlist
     */
    public function destroy(Playlist $playlist)
    {
        $this->authorize('delete', $playlist);

        $playlist->delete();

        return response()->json(['message' => 'Playlist deleted successfully']);
    }

    /**
     * Add song to playlist
     */
    public function addSong(Playlist $playlist, Request $request)
    {
        $this->authorize('update', $playlist);

        $data = $request->validate([
            'article_id' => ['required', 'exists:articles,id'],
        ]);

        // Check if song is already in playlist
        $exists = $playlist->items()
            ->where('article_id', $data['article_id'])
            ->exists();

        if ($exists) {
            return response()->json(['message' => 'Song already in playlist'], 409);
        }

        // Get the next rank
        $nextRank = $playlist->items()->max('rank') + 1;

        $playlist->items()->create([
            'article_id' => $data['article_id'],
            'rank' => $nextRank,
        ]);

        return response()->json(['message' => 'Song added to playlist successfully']);
    }

    /**
     * Remove song from playlist
     */
    public function removeSong(Playlist $playlist, Article $article)
    {
        $this->authorize('update', $playlist);

        $item = $playlist->items()
            ->where('article_id', $article->id)
            ->first();

        if (! $item) {
            return response()->json(['message' => 'Song not found in playlist'], 404);
        }

        $item->delete();

        // Reorder remaining items
        $this->reorderPlaylistItems($playlist);

        return response()->json(['message' => 'Song removed from playlist successfully']);
    }

    /**
     * Reorder songs in playlist
     */
    public function reorderSongs(Playlist $playlist, Request $request)
    {
        $this->authorize('update', $playlist);

        $data = $request->validate([
            'items' => ['required', 'array'],
            'items.*.id' => ['required', 'exists:playlist_items,id'],
            'items.*.rank' => ['required', 'integer', 'min:1'],
        ]);

        foreach ($data['items'] as $item) {
            $playlist->items()
                ->where('id', $item['id'])
                ->update(['rank' => $item['rank']]);
        }

        return response()->json(['message' => 'Playlist reordered successfully']);
    }

    /**
     * Helper method to reorder playlist items after deletion
     */
    private function reorderPlaylistItems(Playlist $playlist)
    {
        $items = $playlist->items()->orderBy('rank')->get();

        foreach ($items as $index => $item) {
            $item->update(['rank' => $index + 1]);
        }
    }

    /**
     * Handle the XHR incoming request.
     */
    public function articles(Playlist $playlist)
    {
        $articles = $playlist->articles()->with(ArticleRepository::RELATIONS)->get();

        return ArticleResource::collection($articles);
    }

    public function updateImage(Request $request)
    {
        $data = $request->validate([
            'playlist_id' => ['required', 'exists:playlists,id'],
            'image' => [
                'required',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ]);

        $playlist = Playlist::find($data['playlist_id']);

        if ($request->hasFile('image')) {
            $playlist->image()->forceDelete();

            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $playlist->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $playlist->refresh();

        return new PlaylistResource($playlist);
    }
}
