<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Repositories\ArticleRepository;
use App\Repositories\ChannelRepository;
use App\Repositories\GenreRepository;
use App\Repositories\PlaylistRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    use HasRetriever;

    public function __construct(
        private GenreRepository $genreRepository,
        private UserRepository $userRepository,
        private ChannelRepository $channelRepository,
        private ArticleRepository $articleRepository,
        private PlaylistRepository $playlistRepository,
    ) {}

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        return inertia('app/search', $this->retrieve($request, [
            'genres' => [$this->genreRepository, 'getGenreHasChild'],
            'search' => [$this, 'search'],
        ]));
    }

    public function search(Request $request)
    {
        $query = $request->query('q');
        if (! $query) {
            return null;
        }

        return [
            'artists' => $this->userRepository->searchArtists($query, 12),
            'channels' => $this->channelRepository->searchChannels($query, 12),
            'articles' => $this->articleRepository->searchArticles($query, 12),
            'playlists' => $this->playlistRepository->searchPlaylists($query, 12),
            'genres' => $this->genreRepository->searchGenres($query, 12),
        ];
    }
}
