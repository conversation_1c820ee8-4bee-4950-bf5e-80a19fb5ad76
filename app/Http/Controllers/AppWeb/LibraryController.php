<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Playlist\PlaylistCollection;
use App\Models\Playlist\Playlist;
use App\Repositories\PlaylistRepository;
use Illuminate\Http\Request;

class LibraryController extends Controller
{
    use HasRetriever;

    public function __construct(
        private PlaylistRepository $playlistRepository
    ) {}

    /**
     * Display user's music library
     */
    public function __invoke(Request $request)
    {
        $user = $request->user();

        if (! $user) {
            return redirect()->route('app.discover');
        }

        $resource = $this->retrieve([$request], [
            'user_playlists' => [$this, 'getUserPlaylists'],
            'liked_playlists' => [$this, 'getLikedPlaylists'],
            'recent_playlists' => [$this, 'getRecentPlaylists'],
        ]);

        return inertia('app/library/index', $resource);
    }

    /**
     * Get user's created playlists
     */
    public function getUserPlaylists(Request $request): PlaylistCollection
    {
        $user = $request->user();

        $playlists = Playlist::where('user_id', $user->id)
            ->where('is_system_generated', false)
            ->with(PlaylistRepository::RELATIONS)
            ->latest()
            ->paginate(12);

        return new PlaylistCollection($playlists);
    }

    /**
     * Get user's liked playlists
     */
    public function getLikedPlaylists(Request $request): PlaylistCollection
    {
        $user = $request->user();

        $playlists = Playlist::whereHas('likes', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
            ->with(PlaylistRepository::RELATIONS)
            ->latest()
            ->paginate(12);

        return new PlaylistCollection($playlists);
    }

    /**
     * Get recently played playlists
     */
    public function getRecentPlaylists(Request $request): PlaylistCollection
    {
        $user = $request->user();

        // This would need to be implemented based on play history
        // For now, return empty collection
        $playlists = Playlist::whereHas('articles.plays', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
            ->with(PlaylistRepository::RELATIONS)
            ->latest()
            ->limit(6)
            ->get();

        return new PlaylistCollection($playlists);
    }

    /**
     * Get user playlists for AJAX requests
     */
    public function playlists(Request $request): PlaylistCollection
    {
        return $this->getUserPlaylists($request);
    }
}
