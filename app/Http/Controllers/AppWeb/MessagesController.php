<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Message\ConversationCollection;
use App\Http\Resources\Message\ConversationResource;
use App\Http\Resources\Message\MessageCollection;
use App\Http\Resources\Message\MessageResource;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;

class MessagesController extends Controller
{
    use HasRetriever;

    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Display user's conversations
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        $resource = $this->retrieve([$request], [
            'conversations' => [$this, 'getConversations'],
        ]);

        return inertia('app/messages/index', $resource);
    }

    /**
     * Get user's conversations
     */
    public function getConversations(Request $request): ConversationCollection
    {
        $user = $request->user();
        
        $conversations = Conversation::where('user1_id', $user->id)
            ->orWhere('user2_id', $user->id)
            ->with(['user1', 'user2', 'messages' => function ($query) {
                $query->latest()->limit(1);
            }])
            ->orderByDesc('last_message_at')
            ->paginate(20);

        return new ConversationCollection($conversations);
    }

    /**
     * Show a specific conversation
     */
    public function show(Conversation $conversation, Request $request)
    {
        $user = $request->user();
        
        // Check if user is part of this conversation
        if ($conversation->user1_id !== $user->id && $conversation->user2_id !== $user->id) {
            abort(403, 'Unauthorized access to conversation');
        }

        $resource = $this->retrieve([$request, $conversation], [
            'conversation' => [$this, 'getConversation'],
            'messages' => [$this, 'getMessages'],
        ]);

        // Mark messages as read
        $conversation->messages()
            ->where('recipient_id', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return inertia('app/messages/show', $resource);
    }

    /**
     * Get conversation details
     */
    public function getConversation(Request $request, Conversation $conversation): ConversationResource
    {
        return new ConversationResource($conversation);
    }

    /**
     * Get messages for a conversation
     */
    public function getMessages(Request $request, Conversation $conversation): MessageCollection
    {
        $messages = $conversation->messages()
            ->with(['sender', 'recipient'])
            ->orderBy('created_at')
            ->paginate(50);

        return new MessageCollection($messages);
    }

    /**
     * Send a message
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'recipient_id' => ['required', 'exists:users,id'],
            'content' => ['required', 'string', 'min:1', 'max:1000'],
        ]);

        $user = $request->user();
        $recipient = User::findOrFail($data['recipient_id']);

        // Prevent sending messages to self
        if ($user->id === $recipient->id) {
            abort(400, 'Cannot send message to yourself');
        }

        // Find or create conversation
        $conversation = Conversation::findOrCreateBetween($user, $recipient);

        // Create message
        $message = $conversation->messages()->create([
            'sender_id' => $user->id,
            'recipient_id' => $recipient->id,
            'content' => $data['content'],
        ]);

        // Update conversation last message time
        $conversation->updateLastMessageTime();

        return new MessageResource($message);
    }

    /**
     * Start a new conversation
     */
    public function startConversation(User $user, Request $request)
    {
        $currentUser = $request->user();
        
        // Prevent starting conversation with self
        if ($currentUser->id === $user->id) {
            abort(400, 'Cannot start conversation with yourself');
        }

        // Find or create conversation
        $conversation = Conversation::findOrCreateBetween($currentUser, $user);

        return redirect()->route('app.messages.show', $conversation);
    }

    /**
     * Get unread messages count
     */
    public function unreadCount(Request $request)
    {
        $user = $request->user();
        
        $count = Message::where('recipient_id', $user->id)
            ->whereNull('read_at')
            ->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Mark conversation as read
     */
    public function markAsRead(Conversation $conversation, Request $request)
    {
        $user = $request->user();
        
        // Check if user is part of this conversation
        if ($conversation->user1_id !== $user->id && $conversation->user2_id !== $user->id) {
            abort(403, 'Unauthorized access to conversation');
        }

        $conversation->messages()
            ->where('recipient_id', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return response()->json(['message' => 'Conversation marked as read']);
    }
}
