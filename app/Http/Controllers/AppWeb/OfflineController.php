<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Article\ArticleCollection;
use App\Http\Resources\Playlist\PlaylistCollection;
use App\Models\Article;
use App\Models\Playlist\Playlist;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class OfflineController extends Controller
{
    use HasRetriever;

    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Display user's offline content
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        $resource = $this->retrieve([$request], [
            'downloaded_articles' => [$this, 'getDownloadedArticles'],
            'downloaded_playlists' => [$this, 'getDownloadedPlaylists'],
            'download_queue' => [$this, 'getDownloadQueue'],
        ]);

        return inertia('app/offline/index', $resource);
    }

    /**
     * Get user's downloaded articles
     */
    public function getDownloadedArticles(Request $request): ArticleCollection
    {
        $user = $request->user();
        
        $downloadedIds = $this->getUserDownloadedArticles($user);
        
        $articles = Article::whereIn('id', $downloadedIds)
            ->with(['channel', 'user', 'audio', 'image'])
            ->get();

        return new ArticleCollection($articles);
    }

    /**
     * Get user's downloaded playlists
     */
    public function getDownloadedPlaylists(Request $request): PlaylistCollection
    {
        $user = $request->user();
        
        $downloadedIds = $this->getUserDownloadedPlaylists($user);
        
        $playlists = Playlist::whereIn('id', $downloadedIds)
            ->with(['user', 'image', 'articles'])
            ->get();

        return new PlaylistCollection($playlists);
    }

    /**
     * Get user's download queue
     */
    public function getDownloadQueue(Request $request)
    {
        $user = $request->user();
        $queue = Cache::get("download_queue_{$user->id}", []);
        
        return response()->json($queue);
    }

    /**
     * Download an article for offline use
     */
    public function downloadArticle(Article $article, Request $request)
    {
        $user = $request->user();
        
        if (!$article->audio) {
            return response()->json(['error' => 'Article has no audio'], 400);
        }

        // Check if already downloaded
        $downloaded = $this->getUserDownloadedArticles($user);
        if (in_array($article->id, $downloaded)) {
            return response()->json(['message' => 'Article already downloaded']);
        }

        // Add to download queue
        $this->addToDownloadQueue($user, 'article', $article->id);

        // Mark as downloaded (in real implementation, this would be done after actual download)
        $this->markAsDownloaded($user, 'article', $article->id);

        return response()->json(['message' => 'Article queued for download']);
    }

    /**
     * Download a playlist for offline use
     */
    public function downloadPlaylist(Playlist $playlist, Request $request)
    {
        $user = $request->user();
        
        // Check if already downloaded
        $downloaded = $this->getUserDownloadedPlaylists($user);
        if (in_array($playlist->id, $downloaded)) {
            return response()->json(['message' => 'Playlist already downloaded']);
        }

        // Add playlist and all its articles to download queue
        $this->addToDownloadQueue($user, 'playlist', $playlist->id);
        
        foreach ($playlist->articles as $article) {
            if ($article->audio) {
                $this->addToDownloadQueue($user, 'article', $article->id);
                $this->markAsDownloaded($user, 'article', $article->id);
            }
        }

        $this->markAsDownloaded($user, 'playlist', $playlist->id);

        return response()->json(['message' => 'Playlist queued for download']);
    }

    /**
     * Remove downloaded content
     */
    public function removeDownload(Request $request)
    {
        $data = $request->validate([
            'type' => ['required', 'in:article,playlist'],
            'id' => ['required', 'string'],
        ]);

        $user = $request->user();
        $this->removeFromDownloaded($user, $data['type'], $data['id']);

        return response()->json(['message' => 'Download removed']);
    }

    /**
     * Get download status for items
     */
    public function getDownloadStatus(Request $request)
    {
        $data = $request->validate([
            'articles' => ['array'],
            'articles.*' => ['string'],
            'playlists' => ['array'],
            'playlists.*' => ['string'],
        ]);

        $user = $request->user();
        $downloadedArticles = $this->getUserDownloadedArticles($user);
        $downloadedPlaylists = $this->getUserDownloadedPlaylists($user);

        $status = [];

        if (isset($data['articles'])) {
            foreach ($data['articles'] as $articleId) {
                $status['articles'][$articleId] = in_array($articleId, $downloadedArticles);
            }
        }

        if (isset($data['playlists'])) {
            foreach ($data['playlists'] as $playlistId) {
                $status['playlists'][$playlistId] = in_array($playlistId, $downloadedPlaylists);
            }
        }

        return response()->json($status);
    }

    /**
     * Clear all downloads
     */
    public function clearAll(Request $request)
    {
        $user = $request->user();
        
        Cache::forget("downloaded_articles_{$user->id}");
        Cache::forget("downloaded_playlists_{$user->id}");
        Cache::forget("download_queue_{$user->id}");

        return response()->json(['message' => 'All downloads cleared']);
    }

    // Helper methods

    private function getUserDownloadedArticles(User $user): array
    {
        return Cache::get("downloaded_articles_{$user->id}", []);
    }

    private function getUserDownloadedPlaylists(User $user): array
    {
        return Cache::get("downloaded_playlists_{$user->id}", []);
    }

    private function addToDownloadQueue(User $user, string $type, string $id): void
    {
        $queue = Cache::get("download_queue_{$user->id}", []);
        $queue[] = [
            'type' => $type,
            'id' => $id,
            'status' => 'pending',
            'added_at' => now()->toISOString(),
        ];
        Cache::put("download_queue_{$user->id}", $queue, now()->addDays(7));
    }

    private function markAsDownloaded(User $user, string $type, string $id): void
    {
        $key = "downloaded_{$type}s_{$user->id}";
        $downloaded = Cache::get($key, []);
        
        if (!in_array($id, $downloaded)) {
            $downloaded[] = $id;
            Cache::put($key, $downloaded, now()->addDays(30));
        }
    }

    private function removeFromDownloaded(User $user, string $type, string $id): void
    {
        $key = "downloaded_{$type}s_{$user->id}";
        $downloaded = Cache::get($key, []);
        
        $downloaded = array_filter($downloaded, fn($item) => $item !== $id);
        Cache::put($key, array_values($downloaded), now()->addDays(30));
    }
}
