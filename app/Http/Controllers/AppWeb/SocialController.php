<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Activity\ActivityCollection;
use App\Http\Resources\User\UserCollection;
use App\Models\Activity;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SocialController extends Controller
{
    use HasRetriever;

    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Display social feed
     */
    public function feed(Request $request)
    {
        $user = $request->user();
        
        $resource = $this->retrieve([$request], [
            'activities' => [$this, 'getFeedActivities'],
            'suggested_users' => [$this, 'getSuggestedUsers'],
            'trending_artists' => [$this, 'getTrendingArtists'],
        ]);

        return inertia('app/social/feed', $resource);
    }

    /**
     * Get feed activities
     */
    public function getFeedActivities(Request $request): ActivityCollection
    {
        $user = $request->user();
        
        // Get activities from followed users and own activities
        $followingIds = $user->followings()->pluck('following_id')->toArray();
        $followingIds[] = $user->id; // Include own activities

        $activities = Activity::whereIn('user_id', $followingIds)
            ->with(['user', 'subject'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return new ActivityCollection($activities);
    }

    /**
     * Get suggested users to follow
     */
    public function getSuggestedUsers(Request $request): UserCollection
    {
        $user = $request->user();
        
        // Get users not already followed, with recent activity
        $followingIds = $user->followings()->pluck('following_id')->toArray();
        $followingIds[] = $user->id; // Exclude self

        $suggestedUsers = User::whereNotIn('id', $followingIds)
            ->where('role', 'artist')
            ->withCount(['articles', 'followers'])
            ->having('articles_count', '>', 0)
            ->orderBy('followers_count', 'desc')
            ->limit(10)
            ->get();

        return new UserCollection($suggestedUsers);
    }

    /**
     * Get trending artists
     */
    public function getTrendingArtists(Request $request): UserCollection
    {
        // Get artists with most plays in the last 7 days
        $trendingArtists = User::where('role', 'artist')
            ->withCount(['articles as recent_plays_count' => function ($query) {
                $query->whereHas('plays', function ($playsQuery) {
                    $playsQuery->where('created_at', '>=', now()->subDays(7));
                });
            }])
            ->having('recent_plays_count', '>', 0)
            ->orderBy('recent_plays_count', 'desc')
            ->limit(10)
            ->get();

        return new UserCollection($trendingArtists);
    }

    /**
     * Discover new users
     */
    public function discover(Request $request)
    {
        $resource = $this->retrieve([$request], [
            'featured_artists' => [$this, 'getFeaturedArtists'],
            'new_artists' => [$this, 'getNewArtists'],
            'popular_artists' => [$this, 'getPopularArtists'],
        ]);

        return inertia('app/social/discover', $resource);
    }

    /**
     * Get featured artists
     */
    public function getFeaturedArtists(Request $request): UserCollection
    {
        $featuredArtists = User::where('role', 'artist')
            ->where('verified', true)
            ->withCount(['articles', 'followers'])
            ->having('articles_count', '>', 5)
            ->orderBy('followers_count', 'desc')
            ->limit(8)
            ->get();

        return new UserCollection($featuredArtists);
    }

    /**
     * Get new artists
     */
    public function getNewArtists(Request $request): UserCollection
    {
        $newArtists = User::where('role', 'artist')
            ->where('created_at', '>=', now()->subDays(30))
            ->withCount(['articles'])
            ->having('articles_count', '>', 0)
            ->orderBy('created_at', 'desc')
            ->limit(12)
            ->get();

        return new UserCollection($newArtists);
    }

    /**
     * Get popular artists
     */
    public function getPopularArtists(Request $request): UserCollection
    {
        $popularArtists = User::where('role', 'artist')
            ->withCount(['followers', 'articles'])
            ->having('articles_count', '>', 0)
            ->orderBy('followers_count', 'desc')
            ->limit(20)
            ->get();

        return new UserCollection($popularArtists);
    }

    /**
     * Follow/unfollow a user
     */
    public function toggleFollow(User $targetUser, Request $request)
    {
        $user = $request->user();

        if ($user->id === $targetUser->id) {
            return response()->json(['error' => 'Cannot follow yourself'], 400);
        }

        $isFollowing = $user->followings()->where('following_id', $targetUser->id)->exists();

        if ($isFollowing) {
            $user->followings()->detach($targetUser->id);
            $action = 'unfollowed';
        } else {
            $user->followings()->attach($targetUser->id);
            $action = 'followed';
            
            // Create activity
            Activity::create([
                'user_id' => $user->id,
                'type' => 'follow',
                'subject_type' => User::class,
                'subject_id' => $targetUser->id,
                'data' => [
                    'target_user_name' => $targetUser->name,
                ],
            ]);
        }

        return response()->json([
            'action' => $action,
            'is_following' => !$isFollowing,
            'followers_count' => $targetUser->followers()->count(),
        ]);
    }

    /**
     * Get user's followers
     */
    public function followers(User $targetUser, Request $request)
    {
        $followers = $targetUser->followers()
            ->with(['image'])
            ->paginate(20);

        return new UserCollection($followers);
    }

    /**
     * Get user's following
     */
    public function following(User $targetUser, Request $request)
    {
        $following = $targetUser->followings()
            ->with(['image'])
            ->paginate(20);

        return new UserCollection($following);
    }

    /**
     * Share content
     */
    public function share(Request $request)
    {
        $data = $request->validate([
            'type' => ['required', 'in:article,playlist,channel'],
            'id' => ['required', 'string'],
            'message' => ['nullable', 'string', 'max:500'],
        ]);

        $user = $request->user();

        // Create share activity
        Activity::create([
            'user_id' => $user->id,
            'type' => 'share',
            'subject_type' => $this->getModelClass($data['type']),
            'subject_id' => $data['id'],
            'data' => [
                'message' => $data['message'] ?? null,
                'shared_type' => $data['type'],
            ],
        ]);

        return response()->json(['message' => 'Content shared successfully']);
    }

    /**
     * Get notifications
     */
    public function notifications(Request $request)
    {
        $user = $request->user();
        
        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json($notifications);
    }

    /**
     * Mark notifications as read
     */
    public function markNotificationsRead(Request $request)
    {
        $user = $request->user();
        
        $user->unreadNotifications->markAsRead();

        return response()->json(['message' => 'Notifications marked as read']);
    }

    private function getModelClass(string $type): string
    {
        return match ($type) {
            'article' => \App\Models\Article::class,
            'playlist' => \App\Models\Playlist\Playlist::class,
            'channel' => \App\Models\Channel::class,
            default => throw new \InvalidArgumentException("Invalid type: {$type}"),
        };
    }
}
