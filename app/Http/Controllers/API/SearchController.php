<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Search\SearchRequest;
use App\Http\Resources\API\Search\SearchResultResource;
use App\Repositories\ArticleRepository;
use App\Repositories\ChannelRepository;
use App\Repositories\GenreRepository;
use App\Repositories\PlaylistRepository;
use App\Repositories\UserRepository;
use Dedoc\Scramble\Attributes\Group;

/**
 * APIs for searching content across the platform
 */
#[Group('Search', weight: 3)]
class SearchController extends Controller
{
    public function __construct(
        private GenreRepository $genreRepository,
        private UserRepository $userRepository,
        private ChannelRepository $channelRepository,
        private ArticleRepository $articleRepository,
        private PlaylistRepository $playlistRepository,
    ) {}

    /**
     * Search content
     *
     * Search for artists, channels, articles, playlists, and genres across the platform.
     */
    public function search(SearchRequest $request): SearchResultResource
    {
        $startTime = microtime(true);

        $query = $request->getQuery();
        $limit = $request->getLimit();

        $results = [
            'query' => $query,
            'artists' => $this->userRepository->searchArtists($query, $limit),
            'channels' => $this->channelRepository->searchChannels($query, $limit),
            'articles' => $this->articleRepository->searchArticles($query, $limit),
            'playlists' => $this->playlistRepository->searchPlaylists($query, $limit),
            'genres' => $this->genreRepository->searchGenres($query, $limit),
            'search_time' => round((microtime(true) - $startTime) * 1000, 2).'ms',
        ];

        return new SearchResultResource($results);
    }
}
