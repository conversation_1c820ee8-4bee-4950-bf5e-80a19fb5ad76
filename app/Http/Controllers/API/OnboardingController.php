<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Common\SuccessResource;
use Illuminate\Http\Request;

class OnboardingController extends Controller
{
    /**
     * Get user onboarding status
     *
     * Returns the current onboarding state for the authenticated user.
     */
    public function status(Request $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $shouldShowWelcome = $user->shouldShowWelcomePopup();
        $isNewUser = $user->isNewUser();

        return SuccessResource::success('Onboarding status retrieved successfully', [
            'shouldShowWelcome' => $shouldShowWelcome,
            'isNewUser' => $isNewUser,
            'hasCompletedOnboarding' => $user->hasCompletedOnboarding(),
            'lastActivityAt' => $user->last_activity_at?->toISOString(),
            'welcomePopupShownAt' => $user->welcome_popup_shown_at?->toISOString(),
            'onboardingCompletedAt' => $user->onboarding_completed_at?->toISOString(),
        ]);
    }

    /**
     * Mark welcome popup as shown
     *
     * Records that the welcome popup has been displayed to the user.
     */
    public function markWelcomeShown(Request $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $user->markWelcomePopupShown();

        return SuccessResource::success('Welcome popup marked as shown');
    }

    /**
     * Complete user onboarding
     *
     * Marks the user's onboarding process as completed.
     */
    public function completeOnboarding(Request $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $user->markOnboardingCompleted();
        $user->markWelcomePopupShown(); // Also mark welcome as shown

        return SuccessResource::success('Onboarding completed successfully', [
            'onboardingCompletedAt' => $user->fresh()->onboarding_completed_at?->toISOString(),
        ]);
    }

    /**
     * Update user activity
     *
     * Updates the user's last activity timestamp.
     */
    public function updateActivity(Request $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $user->updateLastActivity();

        return SuccessResource::success('Activity updated successfully', [
            'lastActivityAt' => $user->fresh()->last_activity_at?->toISOString(),
        ]);
    }
}
