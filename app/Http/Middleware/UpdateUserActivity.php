<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UpdateUserActivity
{
    /**
     * Handle an incoming request.
     *
     * Updates the authenticated user's last activity timestamp.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only update activity for authenticated users
        if ($request->user()) {
            // Use a job or queue to avoid blocking the response
            // For now, we'll update directly but consider queuing for high-traffic apps
            $this->updateUserActivity($request->user());
        }

        return $response;
    }

    /**
     * Update the user's last activity timestamp.
     */
    private function updateUserActivity($user): void
    {
        try {
            // Only update if the last activity was more than 5 minutes ago
            // to avoid excessive database writes
            if (! $user->last_activity_at || $user->last_activity_at->lt(now()->subMinutes(5))) {
                $user->updateLastActivity();
            }
        } catch (\Exception $e) {
            // Silently fail to avoid disrupting the user experience
            \Log::error('Failed to update user activity', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
