<?php

namespace App\Http\Resources\Message;

use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ConversationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = $request->user();
        $otherUser = $this->getOtherUser($user);
        $lastMessage = $this->messages->first();
        
        return [
            'id' => $this->id,
            'other_user' => new UserResource($otherUser),
            'last_message' => $lastMessage ? new MessageResource($lastMessage) : null,
            'last_message_at' => $this->last_message_at?->toIso8601String(),
            'unread_count' => $this->getUnreadCountForUser($user),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}
