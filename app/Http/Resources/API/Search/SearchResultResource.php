<?php

namespace App\Http\Resources\API\Search;

use App\Http\Resources\API\Article\ArticleCollection;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Http\Resources\API\Playlist\PlaylistCollection;
use App\Http\Resources\API\User\ArtistCollection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Search results resource for API responses
 */
class SearchResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'query' => $this->resource['query'] ?? '',
            'results' => [
                'artists' => new ArtistCollection($this->resource['artists'] ?? []),
                'channels' => new ChannelCollection($this->resource['channels'] ?? []),
                'articles' => new ArticleCollection($this->resource['articles'] ?? []),
                'playlists' => new PlaylistCollection($this->resource['playlists'] ?? []),
                'genres' => $this->transformGenres($this->resource['genres'] ?? []),
            ],
            'meta' => [
                'total_results' => $this->getTotalResults(),
                'search_time' => $this->resource['search_time'] ?? null,
            ],
        ];
    }

    /**
     * Transform genres for API response
     */
    private function transformGenres(array $genres): array
    {
        return collect($genres)->map(function ($genre) {
            return [
                'id' => $genre->id,
                'name' => $genre->name,
                'slug' => $genre->slug,
                'description' => $genre->description,
                'channels_count' => $genre->channels_count ?? 0,
                'articles_count' => $genre->articles_count ?? 0,
            ];
        })->toArray();
    }

    /**
     * Get total results count
     */
    private function getTotalResults(): int
    {
        $artists = count($this->resource['artists'] ?? []);
        $channels = count($this->resource['channels'] ?? []);
        $articles = count($this->resource['articles'] ?? []);
        $genres = count($this->resource['genres'] ?? []);

        return $artists + $channels + $articles + $genres;
    }
}
