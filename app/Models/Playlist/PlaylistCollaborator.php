<?php

namespace App\Models\Playlist;

use App\Models\User;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperPlaylistCollaborator
 */
class PlaylistCollaborator extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'playlist_id',
        'user_id',
        'permission',
        'invited_at',
        'accepted_at',
        'declined_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'invited_at' => 'datetime',
            'accepted_at' => 'datetime',
            'declined_at' => 'datetime',
        ];
    }

    public function playlist(): BelongsTo
    {
        return $this->belongsTo(Playlist::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the collaboration is pending
     */
    public function isPending(): bool
    {
        return $this->invited_at && !$this->accepted_at && !$this->declined_at;
    }

    /**
     * Check if the collaboration is accepted
     */
    public function isAccepted(): bool
    {
        return (bool) $this->accepted_at;
    }

    /**
     * Check if the collaboration is declined
     */
    public function isDeclined(): bool
    {
        return (bool) $this->declined_at;
    }

    /**
     * Accept the collaboration
     */
    public function accept(): void
    {
        $this->update([
            'accepted_at' => now(),
            'declined_at' => null,
        ]);
    }

    /**
     * Decline the collaboration
     */
    public function decline(): void
    {
        $this->update([
            'declined_at' => now(),
            'accepted_at' => null,
        ]);
    }
}
