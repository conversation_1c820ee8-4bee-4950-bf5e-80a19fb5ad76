<?php

namespace App\Models\Playlist;

use App\Models\Article;
use App\Models\File;
use App\Models\Morph\Like;
use App\Models\User;
use App\Observers;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Laravel\Scout\Searchable;

/**
 * @mixin IdeHelperPlaylist
 */
#[ObservedBy([
    Observers\PlaylistObserver::class,
])]
class Playlist extends Model
{
    use HasFactory, HasUlids, Searchable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'is_system_generated',
        'is_public',
        'generation_strategy',
        'generated_at',
    ];

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'user' => $this->user->name,
        ];
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_system_generated' => 'boolean',
            'is_public' => 'boolean',
            'generated_at' => 'datetime',
        ];
    }

    public function image(): MorphOne
    {
        return $this->morphOne(File\Image::class, 'fileable');
    }

    public function scopeRecommendations(Builder $query): Builder
    {
        return $query->where('is_system_generated', true);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the items (intermediate model) in the playlist.
     */
    public function items(): HasMany
    {
        return $this->hasMany(PlaylistItem::class)->orderBy('rank');
    }

    /**
     * Get the articles directly in the playlist, ordered by rank through the pivot table.
     */
    public function articles(): BelongsToMany
    {
        return $this->belongsToMany(Article::class, 'playlist_items')
            ->withPivot('rank') // Make rank accessible
            ->orderByPivot('rank'); // Order by rank in the pivot
    }

    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }
}
