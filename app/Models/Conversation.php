<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperConversation
 */
class Conversation extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'user1_id',
        'user2_id',
        'last_message_at',
    ];

    protected $casts = [
        'last_message_at' => 'datetime',
    ];

    /**
     * Get the first user in the conversation
     */
    public function user1(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user1_id');
    }

    /**
     * Get the second user in the conversation
     */
    public function user2(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user2_id');
    }

    /**
     * Get all messages in this conversation
     */
    public function messages(): Has<PERSON>any
    {
        return $this->hasMany(Message::class)->orderBy('created_at');
    }

    /**
     * Get the other user in the conversation
     */
    public function getOtherUser(User $user): User
    {
        if ($this->user1_id === $user->id) {
            return $this->user2()->first();
        }
        return $this->user1()->first();
    }

    /**
     * Get unread messages count for a specific user
     */
    public function getUnreadCountForUser(User $user): int
    {
        return $this->messages()
            ->where('recipient_id', $user->id)
            ->whereNull('read_at')
            ->count();
    }

    /**
     * Find or create a conversation between two users
     */
    public static function findOrCreateBetween(User $user1, User $user2): self
    {
        // Ensure consistent ordering (smaller ID first)
        $userId1 = $user1->id < $user2->id ? $user1->id : $user2->id;
        $userId2 = $user1->id < $user2->id ? $user2->id : $user1->id;

        return self::firstOrCreate([
            'user1_id' => $userId1,
            'user2_id' => $userId2,
        ]);
    }

    /**
     * Update the last message timestamp
     */
    public function updateLastMessageTime(): void
    {
        $this->update(['last_message_at' => now()]);
    }
}
