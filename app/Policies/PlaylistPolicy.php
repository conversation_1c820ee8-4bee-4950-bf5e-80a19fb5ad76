<?php

namespace App\Policies;

use App\Models\Playlist\Playlist;
use App\Models\User;

class PlaylistPolicy
{
    /**
     * Determine whether the user can view any playlists.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the playlist.
     */
    public function view(User $user, Playlist $playlist): bool
    {
        // Users can view public playlists or their own playlists
        return $playlist->is_public || $playlist->user_id === $user->id;
    }

    /**
     * Determine whether the user can create playlists.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update the playlist.
     */
    public function update(User $user, Playlist $playlist): bool
    {
        // Only the owner can update their playlist
        return $playlist->user_id === $user->id;
    }

    /**
     * Determine whether the user can delete the playlist.
     */
    public function delete(User $user, Playlist $playlist): bool
    {
        // Only the owner can delete their playlist (and it shouldn't be system generated)
        return $playlist->user_id === $user->id && ! $playlist->is_system_generated;
    }

    /**
     * Determine whether the user can restore the playlist.
     */
    public function restore(User $user, Playlist $playlist): bool
    {
        return $playlist->user_id === $user->id;
    }

    /**
     * Determine whether the user can permanently delete the playlist.
     */
    public function forceDelete(User $user, Playlist $playlist): bool
    {
        return $playlist->user_id === $user->id && ! $playlist->is_system_generated;
    }
}
