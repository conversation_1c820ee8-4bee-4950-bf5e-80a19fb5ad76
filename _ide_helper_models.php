<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON>l <<EMAIL>>
 */


namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $user_id
 * @property string $model
 * @property string|null $description
 * @property array<array-key, mixed>|null $json
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereJson($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperActivity {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property string|null $genre_id
 * @property string $channel_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $year
 * @property int|null $track_number
 * @property-read \App\Models\File\Audio|null $audio
 * @property-read \App\Models\Embedding\ArticleAudioEmbedding|null $audioEmbedding
 * @property-read \App\Models\ArticleAudioMetadata|null $audioMetadata
 * @property-read \App\Models\Channel $channel
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Download> $downloads
 * @property-read int|null $downloads_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Featuring> $featurings
 * @property-read int|null $featurings_count
 * @property-read \App\Models\Genre|null $genre
 * @property-read \App\Models\File\Image|null $image
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Like> $likes
 * @property-read int|null $likes_count
 * @property-read \App\Models\Embedding\ArticleMetadataEmbedding|null $metadataEmbedding
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Play> $plays
 * @property-read int|null $plays_count
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereGenreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereTrackNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article whereYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Article withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperArticle {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $article_id
 * @property string $file_id
 * @property float $bitrate
 * @property int $sample_rate
 * @property int $channels
 * @property float $tempo
 * @property float $duration
 * @property int $layer
 * @property int $score
 * @property string|null $encoder_info
 * @property string|null $encoder_settings
 * @property \Carbon\CarbonImmutable|null $date
 * @property int|null $year
 * @property string|null $comment
 * @property array<array-key, mixed>|null $json
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Article $article
 * @property-read \App\Models\File\Audio $audio
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereArticleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereBitrate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereChannels($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereEncoderInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereEncoderSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereJson($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereLayer($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereSampleRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereTempo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioMetadata whereYear($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperArticleAudioMetadata {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $user_id
 * @property string $description
 * @property \App\Enums\UserRole $artist_type
 * @property bool $approved
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $youtube_link
 * @property array<array-key, mixed>|null $social_links
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereApproved($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereArtistType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereSocialLinks($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArtistRequest whereYoutubeLink($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperArtistRequest {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property \App\Enums\ChannelType $type
 * @property string|null $genre_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Article> $articles
 * @property-read int|null $articles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Download> $downloads
 * @property-read int|null $downloads_count
 * @property-read \App\Models\Genre|null $genre
 * @property-read \App\Models\File\Image|null $image
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Like> $likes
 * @property-read int|null $likes_count
 * @property-read float|null $unique_query
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel podcast()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereGenreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Channel withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperChannel {}
}

namespace App\Models{
/**
 * Competition model for artist voting competitions
 *
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property \Illuminate\Support\Carbon $start_date
 * @property \Illuminate\Support\Carbon $end_date
 * @property bool $status
 * @property string $type
 * @property int $stage
 * @property array<array-key, mixed>|null $requirements
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property numeric $vote_price
 * @property bool $auto_approve
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\CompetitionEntry> $entries
 * @property-read int|null $entries_count
 * @property-read bool $is_accepting_entries
 * @property-read bool $is_in_entry_phase
 * @property-read bool $is_in_voting_phase
 * @property-read string $phase_status
 * @property-read \App\Models\File\Image|null $image
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Vote> $votes
 * @property-read int|null $votes_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition acceptingEntries()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition currentlyRelevant()
 * @method static \Database\Factories\CompetitionFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereAutoApprove($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereRequirements($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereStage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition whereVotePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Competition withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCompetition {}
}

namespace App\Models{
/**
 * CompetitionEntry model for tracking artist entries in competitions
 *
 * @property string $id
 * @property string $competition_id
 * @property string $user_id
 * @property string $status
 * @property \Illuminate\Support\Carbon $entry_date
 * @property bool $requirements_met
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $terms_agreed_at
 * @property \Illuminate\Support\Carbon|null $privacy_agreed_at
 * @property \Illuminate\Support\Carbon|null $rules_agreed_at
 * @property string|null $agreement_ip_address
 * @property-read \App\Models\Competition $competition
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry approved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereAgreementIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereCompetitionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereEntryDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry wherePrivacyAgreedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereRequirementsMet($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereRulesAgreedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereTermsAgreedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry withLegalAgreements()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompetitionEntry withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCompetitionEntry {}
}

namespace App\Models\Embedding{
/**
 * 
 *
 * @property string $id
 * @property string $article_id
 * @property string $file_id
 * @property \Pgvector\Laravel\Vector $embedding
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Article $article
 * @property-read \App\Models\File\Audio $audio
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding nearestNeighbors(string $column, ?mixed $value, \Pgvector\Laravel\Distance $distance)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding whereArticleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding whereEmbedding($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleAudioEmbedding whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperArticleAudioEmbedding {}
}

namespace App\Models\Embedding{
/**
 * 
 *
 * @property string $id
 * @property string $article_id
 * @property \Pgvector\Laravel\Vector $embedding
 * @property string $text
 * @property bool $with_metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Article $article
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding whereArticleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding whereEmbedding($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding whereText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ArticleMetadataEmbedding whereWithMetadata($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperArticleMetadataEmbedding {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $article_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Article $article
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring whereArticleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Featuring withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperFeaturing {}
}

namespace App\Models\File{
/**
 * 
 *
 * @property string $id
 * @property string $title
 * @property string $storage
 * @property string $filename_disk
 * @property string $filename_download
 * @property string $type
 * @property int $filesize
 * @property string $location
 * @property string|null $url
 * @property int|null $width
 * @property int|null $height
 * @property int|null $duration
 * @property string|null $description
 * @property string $tags
 * @property \App\Models\ArticleAudioMetadata|null $metadata
 * @property string $fileable_type
 * @property string $fileable_id
 * @property string|null $created_by
 * @property string|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $hls_directory
 * @property-read \App\Models\User|null $createdBy
 * @property-read \App\Models\Embedding\ArticleAudioEmbedding|null $embedding
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $fileable
 * @property-read \App\Models\User|null $updatedBy
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereFileableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereFileableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereFilenameDisk($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereFilenameDownload($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereFilesize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereHlsDirectory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereStorage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio whereWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Audio withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperAudio {}
}

namespace App\Models\File{
/**
 * 
 *
 * @property string $id
 * @property string $title
 * @property string $storage
 * @property string $filename_disk
 * @property string $filename_download
 * @property string $type
 * @property int $filesize
 * @property string $location
 * @property string|null $url
 * @property int|null $width
 * @property int|null $height
 * @property int|null $duration
 * @property string|null $description
 * @property string $tags
 * @property string $metadata
 * @property string $fileable_type
 * @property string $fileable_id
 * @property string|null $created_by
 * @property string|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $hls_directory
 * @property-read \App\Models\User|null $createdBy
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $fileable
 * @property-read \App\Models\User|null $updatedBy
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereFileableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereFileableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereFilenameDisk($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereFilenameDownload($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereFilesize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereHlsDirectory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereStorage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image whereWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Image withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperImage {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $user_id
 * @property string $follower_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $follower
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following whereFollowerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Following whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperFollowing {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string $category
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Article> $articles
 * @property-read int|null $articles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel> $channels
 * @property-read int|null $channels_count
 * @property-read \App\Models\File\Image|null $image
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Genre withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperGenre {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $type
 * @property string $title
 * @property string $content
 * @property numeric $version
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $effective_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument ofType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereEffectiveDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument whereVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalDocument withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperLegalDocument {}
}

namespace App\Models\Morph{
/**
 * 
 *
 * @property string $id
 * @property string $comment
 * @property string $commentable_type
 * @property string $commentable_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $commentable
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereCommentableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereCommentableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperComment {}
}

namespace App\Models\Morph{
/**
 * 
 *
 * @property int $id
 * @property string $downloadable_type
 * @property string $downloadable_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $downloadable
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download whereDownloadableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download whereDownloadableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Download whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperDownload {}
}

namespace App\Models\Morph{
/**
 * 
 *
 * @property string $id
 * @property string $title
 * @property string $storage
 * @property string $filename_disk
 * @property string $filename_download
 * @property string $type
 * @property int $filesize
 * @property string $location
 * @property string|null $url
 * @property int|null $width
 * @property int|null $height
 * @property int|null $duration
 * @property string|null $description
 * @property string $tags
 * @property string $metadata
 * @property string $fileable_type
 * @property string $fileable_id
 * @property string|null $created_by
 * @property string|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $hls_directory
 * @property-read \App\Models\User|null $createdBy
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $fileable
 * @property-read \App\Models\User|null $updatedBy
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereFileableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereFileableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereFilenameDisk($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereFilenameDownload($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereFilesize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereHlsDirectory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereStorage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File whereWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|File withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperFile {}
}

namespace App\Models\Morph{
/**
 * 
 *
 * @property int $id
 * @property string $likeable_type
 * @property string $likeable_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $likeable
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like whereLikeableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like whereLikeableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Like whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperLike {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $article_id
 * @property string $user_id
 * @property string|null $player_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Article $article
 * @property-read \App\Models\User|null $player
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play whereArticleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Play whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperPlay {}
}

namespace App\Models\Playlist{
/**
 * 
 *
 * @property string $id
 * @property string $user_id
 * @property string $name
 * @property string|null $description
 * @property bool $is_system_generated TRUE if generated by recommendation engine
 * @property string|null $generation_strategy e.g., following, like_similarity, popular
 * @property \Illuminate\Support\Carbon|null $generated_at Timestamp when the system generated this playlist
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Article> $articles
 * @property-read int|null $articles_count
 * @property-read \App\Models\File\Image|null $image
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Playlist\PlaylistItem> $items
 * @property-read int|null $items_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Like> $likes
 * @property-read int|null $likes_count
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist recommendations()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereGeneratedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereGenerationStrategy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereIsSystemGenerated($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Playlist whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperPlaylist {}
}

namespace App\Models\Playlist{
/**
 * 
 *
 * @property string $id
 * @property string $playlist_id
 * @property string $article_id
 * @property int $rank Order of the item within the playlist
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Article $article
 * @property-read \App\Models\Playlist\Playlist $playlist
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem whereArticleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem wherePlaylistId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem whereRank($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlaylistItem whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperPlaylistItem {}
}

namespace App\Models{
/**
 * Subscription model for tracking user subscriptions
 *
 * @property string $id
 * @property string $user_id
 * @property string $plan
 * @property string $status
 * @property \Illuminate\Support\Carbon $start_date
 * @property \Illuminate\Support\Carbon $end_date
 * @property string|null $transaction_id
 * @property numeric $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription wherePlan($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Subscription withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperSubscription {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property \App\Enums\UserRole $role
 * @property string $password
 * @property string|null $phone
 * @property string|null $country_name
 * @property string|null $country_code
 * @property string|null $timezone
 * @property string|null $city
 * @property string|null $region
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property string|null $two_factor_confirmed_at
 * @property string|null $description
 * @property string|null $provider
 * @property string|null $provider_id
 * @property string|null $provider_token
 * @property bool $verified
 * @property \Illuminate\Support\Carbon|null $onboarding_completed_at
 * @property \Illuminate\Support\Carbon|null $last_activity_at
 * @property \Illuminate\Support\Carbon|null $welcome_popup_shown_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Article> $articles
 * @property-read int|null $articles_count
 * @property-read \App\Models\ArtistRequest|null $artistRequest
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel> $channels
 * @property-read int|null $channels_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\CompetitionEntry> $competitionEntries
 * @property-read int|null $competition_entries_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Featuring> $featurings
 * @property-read int|null $featurings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Following> $followers
 * @property-read int|null $followers_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Following> $followings
 * @property-read int|null $followings_count
 * @property-read \App\Models\File\Image|null $image
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Morph\Like> $likes
 * @property-read int|null $likes_count
 * @property-read int $monthly_listeners
 * @property-read int $monthly_plays
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Play> $playings
 * @property-read int|null $playings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Playlist\Playlist> $playlists
 * @property-read int|null $playlists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Play> $plays
 * @property-read int|null $plays_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Playlist\Playlist> $recommendedPlaylists
 * @property-read int|null $recommended_playlists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Subscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Playlist\Playlist> $userCreatedPlaylists
 * @property-read int|null $user_created_playlists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Vote> $votesGiven
 * @property-read int|null $votes_given_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Vote> $votesReceived
 * @property-read int|null $votes_received_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User admins()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User artist()
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCountryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCountryName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLastActivityAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereOnboardingCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereProviderToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorConfirmedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereVerified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereWelcomePopupShownAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperUser {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $user_id
 * @property string $legal_document_id
 * @property string|null $competition_id
 * @property \Illuminate\Support\Carbon $agreed_at
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Competition|null $competition
 * @property-read \App\Models\LegalDocument $legalDocument
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereAgreedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereCompetitionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereLegalDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereUserAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAgreement whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperUserAgreement {}
}

namespace App\Models{
/**
 * Vote model for tracking votes in artist competitions
 *
 * @property string $id
 * @property string $competition_id
 * @property string $user_id
 * @property string $artist_id
 * @property int $vote_count
 * @property bool $paid
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\User $artist
 * @property-read \App\Models\Competition $competition
 * @property-read \App\Models\VoteTransaction|null $transaction
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereArtistId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereCompetitionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote wherePaid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote whereVoteCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vote withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperVote {}
}

namespace App\Models{
/**
 * VoteTransaction model for tracking payment transactions for votes
 *
 * @property string $id
 * @property string $vote_id
 * @property numeric $amount
 * @property string|null $transaction_id
 * @property string $status
 * @property string $provider
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Vote $vote
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction whereVoteId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VoteTransaction withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperVoteTransaction {}
}

