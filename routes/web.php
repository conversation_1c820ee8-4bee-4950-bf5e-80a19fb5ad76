<?php

use App\Http\Controllers;
use App\Http\Controllers\API\OnboardingController;
use App\Http\Controllers\AppWeb;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LoginViewController;
use App\Http\Controllers\MetricsController;
use App\Http\Controllers\ResetPasswordController;
use App\Http\Controllers\Streaming;
use App\Http\Controllers\Streaming\PlayerController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::impersonate();

// Prometheus metrics endpoint
Route::get('/metrics', MetricsController::class)
    ->middleware('metrics.access')
    ->name('metrics');

Route::middleware('auth')
    ->group(function () {
        Route::get('audio/{file}', Streaming\AudioRangeStreamController::class)
            ->name('audio');

        Route::get('hls/{file}/{filename}', Streaming\AudioHlsStreamController::class)
            ->where(['file' => '[a-z0-9]+', 'filename' => '.+'])
            ->name('hls');

        Route::post('player/next-dj-track', [PlayerController::class, 'getNextDjTrack'])
            ->name('player.next-dj-track');
    });

Route::get('/', HomeController::class);

Route::get('/reset-password/{token}', ResetPasswordController::class)
    ->middleware('guest')
    ->name('password.reset');

Route::prefix('socialite')
    ->name('socialite.')
    ->group(function () {
        Route::get('/{provider}/redirect', [Controllers\SocialiteController::class, 'redirect'])->name('redirect');
        Route::get('/{provider}/callback', [Controllers\SocialiteController::class, 'callback'])->name('callback');
    });

Route::get('/app/login', LoginViewController::class)->name('login');

Route::prefix('app')
    ->name('app.')
    ->group(function () {
        // This cannot be protected by auth middleware, cause is the default
        // unauthenticated route redirection
        Route::get('/', AppWeb\DiscoverController::class)
            ->name('discover');

        // Route::get('/podcasts', AppWeb\PodcastsController::class)->name('podcasts');

        Route::get('/favorites', AppWeb\FavoritesController::class)
            ->name('favorites');

        Route::get('/favorites/articles', [AppWeb\FavoritesController::class, 'favoritesArticles'])
            ->middleware(['auth'])
            ->name('favorites.articles');

        // User Library
        Route::get('/library', AppWeb\LibraryController::class)->name('library');
        Route::get('/library/playlists', [AppWeb\LibraryController::class, 'playlists'])
            ->middleware(['auth'])
            ->name('library.playlists');

        // Messages
        Route::middleware(['auth'])->prefix('messages')->group(function () {
            Route::get('/', [AppWeb\MessagesController::class, 'index'])->name('messages.index');
            Route::get('/unread-count', [AppWeb\MessagesController::class, 'unreadCount'])->name('messages.unread-count');
            Route::post('/', [AppWeb\MessagesController::class, 'store'])->name('messages.store');
            Route::get('/start/{user}', [AppWeb\MessagesController::class, 'startConversation'])->name('messages.start');
            Route::get('/{conversation}', [AppWeb\MessagesController::class, 'show'])->name('messages.show');
            Route::post('/{conversation}/mark-read', [AppWeb\MessagesController::class, 'markAsRead'])->name('messages.mark-read');
        });

        // Offline Downloads
        Route::middleware(['auth'])->prefix('offline')->group(function () {
            Route::get('/', [AppWeb\OfflineController::class, 'index'])->name('offline.index');
            Route::post('/download/article/{article}', [AppWeb\OfflineController::class, 'downloadArticle'])->name('offline.download.article');
            Route::post('/download/playlist/{playlist}', [AppWeb\OfflineController::class, 'downloadPlaylist'])->name('offline.download.playlist');
            Route::delete('/remove', [AppWeb\OfflineController::class, 'removeDownload'])->name('offline.remove');
            Route::get('/status', [AppWeb\OfflineController::class, 'getDownloadStatus'])->name('offline.status');
            Route::delete('/clear', [AppWeb\OfflineController::class, 'clearAll'])->name('offline.clear');
        });

        // Social Features
        Route::middleware(['auth'])->prefix('social')->group(function () {
            Route::get('/feed', [AppWeb\SocialController::class, 'feed'])->name('social.feed');
            Route::get('/discover', [AppWeb\SocialController::class, 'discover'])->name('social.discover');
            Route::post('/follow/{user}', [AppWeb\SocialController::class, 'toggleFollow'])->name('social.follow');
            Route::get('/followers/{user}', [AppWeb\SocialController::class, 'followers'])->name('social.followers');
            Route::get('/following/{user}', [AppWeb\SocialController::class, 'following'])->name('social.following');
            Route::post('/share', [AppWeb\SocialController::class, 'share'])->name('social.share');
            Route::get('/notifications', [AppWeb\SocialController::class, 'notifications'])->name('social.notifications');
            Route::post('/notifications/read', [AppWeb\SocialController::class, 'markNotificationsRead'])->name('social.notifications.read');
        });

        // Search and Genre
        Route::prefix('search')
            ->group(function () {
                Route::get('/', AppWeb\SearchController::class)->name('search');

                Route::get('g/{genre}', [AppWeb\GenresController::class, 'show'])->name('search.genres.show');

                Route::get('g/{genre}/articles', [AppWeb\GenresController::class, 'genreArticles'])
                    ->middleware(['auth'])
                    ->name('search.genres.articles');
            });

        // Artists
        Route::prefix('artists')
            ->group(function () {
                // Route::get('/', AppWeb\ArtistesController::class)->name('artists');

                Route::get('/{user}', [AppWeb\ArtistesController::class, 'show'])->name('artists.show');

                // XHR request
                Route::get('/{user}/top-charts', [AppWeb\ArtistesController::class, 'getPopularArticles'])
                    ->middleware(['auth'])
                    ->name('artists.top-charts');
            });

        // Articles
        Route::prefix('articles')
            ->name('articles.')
            ->group(function () {
                // XHR request
                Route::get('/{article}/view', [AppWeb\ArticlesController::class, 'view'])
                    ->middleware(['auth'])
                    ->name('view');
            });

        // Channels
        Route::prefix('channels')
            ->group(function () {
                // Route::get('/', AppWeb\ChannelsController::class)->name('channels');

                Route::get('/{channel}', [AppWeb\ChannelsController::class, 'show'])
                    ->name('channels.show');

                // XHR request
                Route::get('/{channel}/articles', [AppWeb\ChannelsController::class, 'articles'])
                    ->middleware(['auth'])
                    ->name('channels.articles');

                Route::get('/{channel}/download', [AppWeb\ChannelsController::class, 'download'])
                    ->middleware(['auth'])
                    ->name('channels.download');

                // XHR request
                Route::get('/{channel}/view', [AppWeb\ChannelsController::class, 'view'])
                    ->middleware(['auth'])
                    ->name('channels.view');
            });

        // Playlists
        Route::prefix('playlists')
            ->group(function () {
                Route::get('/{playlist}', [AppWeb\PlaylistsController::class, 'show'])
                    ->name('playlists.show');

                // XHR request
                Route::get('/{playlist}/articles', [AppWeb\PlaylistsController::class, 'articles'])
                    ->middleware(['auth'])
                    ->name('playlists.articles');

                // User playlist management
                Route::middleware(['auth'])->group(function () {
                    Route::post('/create', [AppWeb\PlaylistsController::class, 'create'])
                        ->name('playlists.create');

                    Route::put('/{playlist}/update', [AppWeb\PlaylistsController::class, 'update'])
                        ->name('playlists.update');

                    Route::delete('/{playlist}', [AppWeb\PlaylistsController::class, 'destroy'])
                        ->name('playlists.destroy');

                    Route::post('/{playlist}/add-song', [AppWeb\PlaylistsController::class, 'addSong'])
                        ->name('playlists.add-song');

                    Route::delete('/{playlist}/remove-song/{article}', [AppWeb\PlaylistsController::class, 'removeSong'])
                        ->name('playlists.remove-song');

                    Route::put('/{playlist}/reorder', [AppWeb\PlaylistsController::class, 'reorderSongs'])
                        ->name('playlists.reorder');

                    // Collaboration routes
                    Route::post('/{playlist}/invite-collaborator', [AppWeb\PlaylistsController::class, 'inviteCollaborator'])
                        ->name('playlists.invite-collaborator');

                    Route::delete('/{playlist}/remove-collaborator/{collaborator}', [AppWeb\PlaylistsController::class, 'removeCollaborator'])
                        ->name('playlists.remove-collaborator');

                    Route::put('/{playlist}/update-collaborator/{collaborator}', [AppWeb\PlaylistsController::class, 'updateCollaborator'])
                        ->name('playlists.update-collaborator');

                    // Organization routes
                    Route::put('/{playlist}/move-to-folder', [AppWeb\PlaylistsController::class, 'moveToFolder'])
                        ->name('playlists.move-to-folder');

                    Route::put('/{playlist}/update-tags', [AppWeb\PlaylistsController::class, 'updateTags'])
                        ->name('playlists.update-tags');

                    // Bulk operations
                    Route::delete('/bulk-delete', [AppWeb\PlaylistsController::class, 'bulkDelete'])
                        ->name('playlists.bulk-delete');

                    Route::put('/bulk-move-to-folder', [AppWeb\PlaylistsController::class, 'bulkMoveToFolder'])
                        ->name('playlists.bulk-move-to-folder');

                    Route::post('/bulk-download', [AppWeb\PlaylistsController::class, 'bulkDownload'])
                        ->name('playlists.bulk-download');

                    // Duplication and advanced features
                    Route::post('/{playlist}/duplicate', [AppWeb\PlaylistsController::class, 'duplicate'])
                        ->name('playlists.duplicate');
                });
            });

        // Playlist Folders
        Route::prefix('playlist-folders')
            ->middleware(['auth'])
            ->group(function () {
                Route::post('/create', [AppWeb\PlaylistFoldersController::class, 'create'])
                    ->name('playlist-folders.create');

                Route::put('/{folder}/update', [AppWeb\PlaylistFoldersController::class, 'update'])
                    ->name('playlist-folders.update');

                Route::delete('/{folder}', [AppWeb\PlaylistFoldersController::class, 'destroy'])
                    ->name('playlist-folders.destroy');
            });

        // Plays
        Route::middleware(['auth'])
            ->put('plays/article/{article}', [AppWeb\PlaysController::class, 'storeArticlePlay'])
            ->name('plays.article');

        // Following
        Route::middleware(['auth'])
            ->put('followings/toggle/{user}', [AppWeb\FollowingsController::class, 'toggle'])
            ->name('followings.toggle');

        // Like
        Route::prefix('like')
            ->name('like.')
            ->middleware(['auth'])
            ->group(function () {
                Route::put('/channel/{channel}', [AppWeb\LikeController::class, 'channel'])->name('channel');

                Route::put('/article/{article}', [AppWeb\LikeController::class, 'article'])->name('article');

                Route::put('/playlist/{playlist}', [AppWeb\LikeController::class, 'playlist'])->name('playlist');
            });

        Route::prefix('notifications')
            ->name('notifications.')
            ->middleware(['auth'])
            ->group(function () {
                Route::post('/clear', [AppWeb\NotificationsController::class, 'clear'])->name('clear');

                Route::delete('/{notificationId}/destroy', [AppWeb\NotificationsController::class, 'destroy'])->name('destroy');
            });

        // Comments (XHR Request)
        Route::prefix('comments')
            ->middleware(['auth'])
            ->name('comments.')
            ->group(function () {

                Route::put('{comment}/update', [AppWeb\CommentsController::class, 'update'])
                    ->name('update');

                Route::delete('{comment}/delete', [AppWeb\CommentsController::class, 'delete'])
                    ->name('delete');

                Route::prefix('{article}/article')
                    ->group(function () {
                        Route::get('/', [AppWeb\CommentsController::class, 'article'])->name('article');

                        Route::post('/create', [AppWeb\CommentsController::class, 'articleCommentStore'])
                            ->name('article.create');
                    });

                Route::prefix('{channel}/channel')
                    ->group(function () {
                        Route::get('/', [AppWeb\CommentsController::class, 'channel'])->name('channel');

                        Route::post('/create', [AppWeb\CommentsController::class, 'ChannelCommentStore'])
                            ->name('channel.create');
                    });
            });

        // Settings
        Route::prefix('s')
            ->name('settings.')
            ->middleware(['auth'])
            ->group(function () {

                Route::get('/account', AppWeb\Settings\AccountController::class)->name('account');

                // Update auth user image profile
                Route::post('/account/image-profile', [AppWeb\Settings\AccountController::class, 'updateImageProfile'])
                    ->name('account.image-profile');

                Route::post('/artist-request', [AppWeb\Settings\ArtistRequestController::class, 'store'])->name('artist-request');
            });

        // Voting and Competitions
        Route::prefix('voting')
            ->name('voting.')
            ->group(function () {
                Route::get('/', [AppWeb\CompetitionController::class, 'index'])->name('index');

                Route::get('/competition/{competition}/leaderboard', [AppWeb\CompetitionController::class, 'leaderboard'])
                    ->name('leaderboard');

                Route::get('/competition/{competition}/artist/{artist}', [AppWeb\CompetitionController::class, 'artistProfile'])
                    ->name('artist');

                // Artist entry routes (requires auth and artist role)
                Route::middleware(['auth'])
                    ->group(function () {
                        Route::get('/competition/{competition}/entry', [AppWeb\CompetitionController::class, 'entryForm'])
                            ->name('entry.form');

                        Route::post('/competition/{competition}/entry', [AppWeb\CompetitionController::class, 'submitEntry'])
                            ->name('entry.submit');
                    });

                // Voting routes (requires auth)
                Route::middleware(['auth'])
                    ->group(function () {
                        Route::post('/competition/{competition}/vote/{artist}', [AppWeb\PaymentController::class, 'initializePayment'])
                            ->name('vote');

                        Route::get('/competition/{competition}/share/{artist}', [AppWeb\VoteController::class, 'share'])
                            ->name('share');

                        // Payment routes
                        Route::get('/payment/mock', [AppWeb\PaymentController::class, 'mockPaymentPage'])
                            ->name('payment.mock');

                        Route::post('/payment/mock/process', [AppWeb\PaymentController::class, 'processMockPayment'])
                            ->name('payment.mock.process');

                        Route::get('/payment/callback', [AppWeb\PaymentController::class, 'handlePaymentCallback'])
                            ->name('payment.callback');

                        // Subscription routes
                        Route::get('/subscription/checkout', [AppWeb\PaymentController::class, 'subscriptionCheckout'])
                            ->name('subscription.checkout');
                    });
            });

        // Legal Documents
        Route::prefix('legal')
            ->name('legal.')
            ->group(function () {
                Route::get('/documents', [AppWeb\LegalDocumentController::class, 'getActiveDocuments'])
                    ->name('documents');
                Route::get('/document/{type}', [AppWeb\LegalDocumentController::class, 'getDocument'])
                    ->name('document');
                Route::get('/view/{type}', [AppWeb\LegalDocumentController::class, 'showDocument'])
                    ->name('view');
            });

        // User Onboarding
        Route::prefix('user/onboarding')
            ->group(function () {
                Route::get('/status', [OnboardingController::class, 'status']);
                Route::post('/welcome-shown', [OnboardingController::class, 'markWelcomeShown']);
                Route::post('/complete', [OnboardingController::class, 'completeOnboarding']);
            });

        // User Activity
        Route::post('/user/activity/update', [OnboardingController::class, 'updateActivity']);
    });
